import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Sparkles, Calendar, CheckCircle, 
  Shield, Briefcase,
  Users, Heart,
  ShoppingBag
} from 'lucide-react';
import { But<PERSON> } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

interface IndustrialBookingFormData {
  facilityType: string;
  facilitySize: string;
  serviceFrequency: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  addOns: string[];
  companyName: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
}

const BrandAlignedIndustrialForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, ] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState<IndustrialBookingFormData>({
    facilityType: '',
    facilitySize: '',
    serviceFrequency: 'one-time',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    addOns: [],
    companyName: '',
    contactName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
  });

  const calculatePrice = () => {
    let basePrice = 0;
    if (formData.facilitySize === 'small') basePrice = 250;
    else if (formData.facilitySize === 'medium') basePrice = 450;
    else if (formData.facilitySize === 'large') basePrice = 750;
    else if (formData.facilitySize === 'xl') basePrice = 1200;

    const addOnPrice = formData.addOns.length * 75; // Industrial pricing is higher
    return basePrice + addOnPrice;
  };

  const facilityTypes = [
    { id: 'manufacturing', name: 'Manufacturing Plant', icon: <Building className="w-6 h-6" />, description: 'Production facilities' },
    { id: 'warehouse', name: 'Warehouse', icon: <Sparkles className="w-6 h-6" />, description: 'Storage & distribution' },
    { id: 'factory', name: 'Factory', icon: <ShoppingBag className="w-6 h-6" />, description: 'Heavy production' },
    { id: 'processing', name: 'Processing Plant', icon: <Heart className="w-6 h-6" />, description: 'Chemical & food processing' },
  ];

  const facilitySizes = [
    { id: 'small', name: 'Under 5,000 sq ft', description: 'Small facilities' },
    { id: 'medium', name: '5,000 - 25,000 sq ft', description: 'Medium facilities' },
    { id: 'large', name: '25,000 - 100,000 sq ft', description: 'Large facilities' },
    { id: 'xl', name: '100,000+ sq ft', description: 'Mega facilities' },
  ];
  
  const serviceFrequencies = [
    { id: 'one-time', name: 'One-Time' },
    { id: 'weekly', name: 'Weekly' },
    { id: 'bi-weekly', name: 'Bi-Weekly' },
    { id: 'monthly', name: 'Monthly' },
  ];

  const addOnServices = [
    { id: 'equipment', name: 'Equipment Deep Clean', description: 'Heavy machinery cleaning', price: 200, icon: <Sparkles className="w-5 h-5" /> },
    { id: 'floor-coating', name: 'Floor Coating Removal', description: 'Industrial floor treatments', price: 300, icon: <Users className="w-5 h-5" /> },
    { id: 'hazmat', name: 'Hazmat Cleanup', description: 'Safe handling of industrial waste', price: 500, icon: <Shield className="w-5 h-5" /> },
    { id: 'pressure-wash', name: 'Pressure Washing', description: 'High-pressure exterior cleaning', price: 250, icon: <Briefcase className="w-5 h-5" /> },
  ];

  const timeSlots = [
    { id: 'after-hours', name: 'After 6:00 PM', label: 'After Hours', popular: true },
    { id: 'morning', name: '8:00 AM - 11:00 AM', label: 'Morning' },
    { id: 'midday', name: '11:00 AM - 2:00 PM', label: 'Midday' },
    { id: 'weekend', name: 'Weekend (Sat/Sun)', label: 'Weekend' },
  ];

  const isStepValid = (step: number) => {
    switch (step) {
      case 0: return !!formData.facilityType && !!formData.facilitySize && !!formData.serviceFrequency;
      case 1: return !!formData.preferredDate && !!formData.preferredTime;
      case 2: return true;
      case 3: return !!formData.companyName && !!formData.contactName && !!formData.email && !!formData.phone && !!formData.address && !!formData.city && !!formData.zipCode;
      default: return false;
    }
  };

  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/industrial' } });
      return;
    }
    setShowPaymentModal(true);
  };
  
  const steps = [
    { title: 'Facility Details', subtitle: 'Tell us about your facility', icon: <Building className="w-6 h-6" /> },
    { title: 'Schedule', subtitle: 'Choose date and time', icon: <Calendar className="w-6 h-6" /> },
    { title: 'Industrial Add-ons', subtitle: 'Enhance your cleaning', icon: <Sparkles className="w-6 h-6" /> },
    { title: 'Contact Info', subtitle: 'Finalize your quote', icon: <CheckCircle className="w-6 h-6" /> }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm sticky top-0 z-40 p-4">
        <h1 className="text-2xl font-bold text-gray-900 text-center">Book Industrial Cleaning</h1>
      </div>

      <div className="bg-white border-b p-4">
        <div className="flex justify-between items-center max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <React.Fragment key={index}>
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= index ? 'bg-brand-600 text-white' : 'bg-gray-200 text-gray-600'}`}>
                  {currentStep > index ? <CheckCircle size={20} /> : index + 1}
                </div>
                <div className="ml-2 hidden md:block">
                  <p className={`font-medium ${currentStep >= index ? 'text-brand-600' : 'text-gray-500'}`}>{step.title}</p>
                </div>
              </div>
              {index < steps.length - 1 && <div className="flex-1 h-0.5 mx-4 bg-gray-200" />}
            </React.Fragment>
          ))}
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <AnimatePresence mode="wait">
              {/* Step 0: Service Details */}
              {currentStep === 0 && (
                <motion.div key="step0" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                  <h2 className="text-xl font-bold mb-4">Facility Details</h2>
                  {/* Facility Type */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {facilityTypes.map(type => (
                      <button key={type.id} onClick={() => setFormData({...formData, facilityType: type.id})} className={`p-4 border rounded-lg ${formData.facilityType === type.id ? 'border-brand-600' : ''}`}>
                        {type.icon} {type.name}
                      </button>
                    ))}
                  </div>
                  {/* Facility Size */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    {facilitySizes.map(size => (
                       <button key={size.id} onClick={() => setFormData({...formData, facilitySize: size.id})} className={`p-4 border rounded-lg ${formData.facilitySize === size.id ? 'border-brand-600' : ''}`}>
                        {size.name}
                      </button>
                    ))}
                  </div>
                   {/* Service Frequency */}
                   <div className="grid grid-cols-2 gap-4 mb-4">
                    {serviceFrequencies.map(freq => (
                       <button key={freq.id} onClick={() => setFormData({...formData, serviceFrequency: freq.id})} className={`p-4 border rounded-lg ${formData.serviceFrequency === freq.id ? 'border-brand-600' : ''}`}>
                        {freq.name}
                      </button>
                    ))}
                  </div>
                  <Button onClick={() => setCurrentStep(1)} disabled={!isStepValid(0)}>Next</Button>
                </motion.div>
              )}

              {/* Step 1: Schedule */}
              {currentStep === 1 && (
                 <motion.div key="step1" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                   <h2 className="text-xl font-bold mb-4">Schedule</h2>
                   <input type="date" onChange={e => setFormData({...formData, preferredDate: e.target.value})} className="p-2 border rounded-lg w-full mb-4" />
                   <div className="grid grid-cols-2 gap-4 mb-4">
                      {timeSlots.map(slot => (
                        <button key={slot.id} onClick={() => setFormData({...formData, preferredTime: slot.id})} className={`p-4 border rounded-lg ${formData.preferredTime === slot.id ? 'border-brand-600' : ''}`}>
                          {slot.name}
                        </button>
                      ))}
                   </div>
                   <Button onClick={() => setCurrentStep(0)}>Back</Button>
                   <Button onClick={() => setCurrentStep(2)} disabled={!isStepValid(1)} className="ml-2">Next</Button>
                 </motion.div>
              )}
              
              {/* Step 2: Add-ons */}
              {currentStep === 2 && (
                <motion.div key="step2" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                  <h2 className="text-xl font-bold mb-4">Add-ons</h2>
                   <div className="grid grid-cols-2 gap-4 mb-4">
                      {addOnServices.map(addon => (
                        <button key={addon.id} onClick={() => {
                          const newAddOns = formData.addOns.includes(addon.id)
                            ? formData.addOns.filter(a => a !== addon.id)
                            : [...formData.addOns, addon.id];
                          setFormData({ ...formData, addOns: newAddOns });
                        }} className={`p-4 border rounded-lg ${formData.addOns.includes(addon.id) ? 'border-brand-600' : ''}`}>
                          {addon.name} (+${addon.price})
                        </button>
                      ))}
                   </div>
                   <Button onClick={() => setCurrentStep(1)}>Back</Button>
                   <Button onClick={() => setCurrentStep(3)} className="ml-2">Next</Button>
                </motion.div>
              )}

              {/* Step 3: Contact Info */}
              {currentStep === 3 && (
                <motion.div key="step3" initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -20 }}>
                  <h2 className="text-xl font-bold mb-4">Contact Information</h2>
                  <input type="text" placeholder="Company Name" onChange={e => setFormData({...formData, companyName: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="text" placeholder="Contact Name" onChange={e => setFormData({...formData, contactName: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="email" placeholder="Email" onChange={e => setFormData({...formData, email: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="tel" placeholder="Phone" onChange={e => setFormData({...formData, phone: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="text" placeholder="Address" onChange={e => setFormData({...formData, address: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="text" placeholder="City" onChange={e => setFormData({...formData, city: e.target.value})} className="p-2 border rounded-lg w-full mb-2" />
                  <input type="text" placeholder="ZIP Code" onChange={e => setFormData({...formData, zipCode: e.target.value})} className="p-2 border rounded-lg w-full mb-4" />
                  <Button onClick={() => setCurrentStep(2)}>Back</Button>
                  <Button onClick={handleSubmit} disabled={!isStepValid(3) || isSubmitting} className="ml-2">
                    {isSubmitting ? 'Submitting...' : 'Submit for Quote'}
                  </Button>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
          <div className="lg:sticky lg:top-32 h-fit">
            <div className="bg-white rounded-2xl shadow-soft p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quote Summary</h3>
                <p className="text-3xl font-bold text-gray-900">${calculatePrice()}</p>
                <p className="text-gray-600">Estimated price.</p>
            </div>
          </div>
        </div>
      </div>

      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description="Industrial Cleaning Service"
          formData={{...formData, serviceType: 'industrial'}}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedIndustrialForm; 
