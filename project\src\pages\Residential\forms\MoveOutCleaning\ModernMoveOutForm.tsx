import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Castle, Warehouse, Gift, Zap, Users,
  Mail, Phone, MapPin, Package, Shield, Truck
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';

import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  propertySize: string;
  bedrooms: string;
  bathrooms: string;
  moveType: string;
  depositGuarantee: boolean;
  landlordRequirements: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernMoveOutForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    propertySize: '',
    bedrooms: '',
    bathrooms: '',
    moveType: '',
    depositGuarantee: false,
    landlordRequirements: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('moveOutFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('moveOutFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Move-Out Options' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units',
      multiplier: 1.0
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes',
      multiplier: 1.2
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Castle, 
      description: 'Multi-level attached',
      multiplier: 1.15
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: Warehouse, 
      description: '3+ stories, 4+ bedrooms',
      multiplier: 1.5
    }
  ];

  const propertySizes = [
    { id: 'small', name: 'Small (< 1,200 sq ft)', basePrice: 150, multiplier: 0.8 },
    { id: 'medium', name: 'Medium (1,200 - 2,000 sq ft)', basePrice: 220, multiplier: 1.0 },
    { id: 'large', name: 'Large (2,000 - 3,000 sq ft)', basePrice: 300, multiplier: 1.3 },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)', basePrice: 400, multiplier: 1.7 }
  ];

  const moveTypes = [
    { 
      id: 'move-out', 
      name: 'Move-Out Only', 
      price: 0, 
      description: 'Clean before you leave',
      features: ['Deep clean all rooms', 'Kitchen deep clean', 'Bathroom sanitization', 'Floor cleaning', 'Window cleaning']
    },
    { 
      id: 'move-in', 
      name: 'Move-In Only', 
      price: 0, 
      description: 'Clean before you arrive',
      features: ['Sanitize all surfaces', 'Deep clean appliances', 'Fresh start cleaning', 'Move-in ready']
    },
    { 
      id: 'both', 
      name: 'Move-Out + Move-In', 
      price: 50, 
      description: 'Complete transition service',
      features: ['Everything in both services', 'Coordination between locations', 'Priority scheduling', 'Deposit guarantee']
    }
  ];

  const addOnServices = [
    { id: 'deposit-guarantee', name: 'Deposit Back Guarantee', price: 25, icon: Shield },
    { id: 'inside-oven', name: 'Inside Oven Deep Clean', price: 35, icon: Zap },
    { id: 'inside-fridge', name: 'Inside Refrigerator', price: 35, icon: Gift },
    { id: 'interior-windows', name: 'All Interior Windows', price: 50, icon: Sparkles },
    { id: 'garage-cleaning', name: 'Garage Cleaning', price: 60, icon: Home },
    { id: 'basement-cleaning', name: 'Basement Cleaning', price: 45, icon: Building2 },
    { id: 'carpet-cleaning', name: 'Carpet Steam Cleaning', price: 80, icon: Package },
    { id: 'wall-washing', name: 'Wall Washing', price: 70, icon: Users }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code' : '';
      }
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Validate field on change
    const error = validateField(field, value);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const calculateTotalPrice = (): number => {
    const selectedSize = propertySizes.find(size => size.id === formData.propertySize);
    const selectedType = propertyTypes.find(type => type.id === formData.propertyType);
    const selectedMoveType = moveTypes.find(type => type.id === formData.moveType);
    
    let basePrice = selectedSize?.basePrice || 220;
    basePrice *= selectedType?.multiplier || 1.0;
    basePrice += selectedMoveType?.price || 0;
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      const addOn = addOnServices.find(service => service.id === addOnId);
      return total + (addOn?.price || 0);
    }, 0);

    return Math.round(basePrice + addOnTotal);
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.propertySize && formData.bedrooms && formData.bathrooms);
      case 2:
        return !!(formData.moveType);
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.firstName && 
          formData.lastName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode && 
          formData.preferredDate && 
          formData.preferredTime &&
          !Object.keys(validationErrors).length
        );
      default:
        return false;
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use 'residential_move' service type for move-out cleaning
      const serviceType = 'residential_move';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        cleaningType: 'move', // This ensures proper mapping
        frequency: 'one-time', // Move-out is always one-time
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('moveOutBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      const serviceType = 'residential_move';

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          size: formData.propertySize,
          bedrooms: formData.bedrooms,
          bathrooms: formData.bathrooms,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: 'one-time',
          cleaningType: 'move',
          moveType: formData.moveType,
          depositGuarantee: formData.depositGuarantee,
          landlordRequirements: formData.landlordRequirements,
          addOns: formData.addOns || [],
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_move_out_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save move-out booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Move-out booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('moveOutBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `MOC-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Move-Out Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Move-Out Cleaning',
            serviceType: 'Move-Out Cleaning',
            status: 'confirmed',
            message: `Your move-out cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `MOC-${Date.now()}`,
            confirmationNumber: `MOC-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Move-Out Cleaning',
          bookingDetails: {
            id: `MOC-${Date.now()}`,
            type: 'Move-Out Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Move-Out Cleaning Service
            </h1>
            <p className="text-gray-600">Professional deep cleaning for your move-out or move-in needs.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property Details */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Tell us about your property</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                                                     <motion.button
                             key={type.id}
                             onClick={() => setFormData({ ...formData, propertyType: type.id })}
                             whileHover={{ scale: 1.02, y: -2 }}
                             whileTap={{ scale: 0.98 }}
                             className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                               formData.propertyType === type.id 
                                 ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                 : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                             }`}
                           >
                             <div className="flex items-center gap-4">
                               <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                 formData.propertyType === type.id 
                                   ? 'bg-emerald-100 text-emerald-800' 
                                   : 'bg-gray-100 text-gray-600'
                               }`}>
                                 <IconComponent className="w-6 h-6" />
                               </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Property Size */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Size</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertySizes.map((size) => (
                                                 <motion.button
                           key={size.id}
                           onClick={() => setFormData({ ...formData, propertySize: size.id })}
                           whileHover={{ scale: 1.02, y: -2 }}
                           whileTap={{ scale: 0.98 }}
                           className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                             formData.propertySize === size.id 
                               ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                               : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                           }`}
                        >
                          <h3 className="font-semibold text-gray-900">{size.name}</h3>
                          <p className="text-sm text-emerald-800 font-medium">Starting at ${size.basePrice}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Bedrooms and Bathrooms */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Bedrooms</label>
                      <GlassmorphismSelect
                        options={[
                          { id: '1', name: '1 Bedroom' },
                          { id: '2', name: '2 Bedrooms' },
                          { id: '3', name: '3 Bedrooms' },
                          { id: '4', name: '4 Bedrooms' },
                          { id: '5+', name: '5+ Bedrooms' }
                        ]}
                        value={formData.bedrooms}
                        onChange={(value) => setFormData({ ...formData, bedrooms: value })}
                        placeholder="Select bedrooms"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Bathrooms</label>
                      <GlassmorphismSelect
                        options={[
                          { id: '1', name: '1 Bathroom' },
                          { id: '1.5', name: '1.5 Bathrooms' },
                          { id: '2', name: '2 Bathrooms' },
                          { id: '2.5', name: '2.5 Bathrooms' },
                          { id: '3', name: '3 Bathrooms' },
                          { id: '3.5', name: '3.5 Bathrooms' },
                          { id: '4+', name: '4+ Bathrooms' }
                        ]}
                        value={formData.bathrooms}
                        onChange={(value) => setFormData({ ...formData, bathrooms: value })}
                        placeholder="Select bathrooms"
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => window.history.back()}>Back</Button>
                                         <Button 
                       onClick={() => setCurrentStep(2)}
                       disabled={!isStepValid(1)}
                       className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                     >
                      Next: Move Options
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Move-Out Options */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Choose your move service</h2>
                  
                  {/* Move Type Selection */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Service Type</label>
                    <div className="grid grid-cols-1 gap-4">
                      {moveTypes.map((type) => (
                                                 <motion.button
                           key={type.id}
                           onClick={() => setFormData({ ...formData, moveType: type.id })}
                           whileHover={{ scale: 1.01, y: -2 }}
                           whileTap={{ scale: 0.99 }}
                           className={`p-6 rounded-xl border-2 text-left transition-all duration-200 ${
                             formData.moveType === type.id 
                               ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                               : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                           }`}
                         >
                           <div className="flex justify-between items-start mb-3">
                             <div>
                               <h3 className="font-semibold text-gray-900 text-lg">{type.name}</h3>
                               <p className="text-gray-600">{type.description}</p>
                               {type.price > 0 && (
                                 <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-sm inline-block mt-2">+${type.price}</p>
                               )}
                             </div>
                             <div className={`p-2 rounded-lg transition-colors duration-200 ${
                               formData.moveType === type.id 
                                 ? 'bg-emerald-100 text-emerald-800' 
                                 : 'bg-gray-100 text-gray-600'
                             }`}>
                               <Truck className="w-6 h-6" />
                             </div>
                           </div>
                           <ul className="text-sm text-gray-700 space-y-1">
                             {type.features.map((feature, index) => (
                               <li key={index} className="flex items-center">
                                 <CheckCircle className="w-4 h-4 text-emerald-600 mr-2" />
                                 {feature}
                               </li>
                             ))}
                          </ul>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Deposit Guarantee Option */}
                  <div className="mb-6">
                    <label className="flex items-center text-gray-900 cursor-pointer">
                                             <input
                         type="checkbox"
                         checked={formData.depositGuarantee || false}
                         onChange={(e) => setFormData({ ...formData, depositGuarantee: e.target.checked })}
                         className="mr-3 w-5 h-5 text-emerald-600 bg-white border-2 border-gray-300 rounded focus:ring-emerald-500"
                       />
                       <div className="flex items-center">
                         <Shield className="w-5 h-5 text-emerald-600 mr-2" />
                        <span className="font-semibold">Deposit Back Guarantee (+$25)</span>
                      </div>
                    </label>
                    <p className="text-sm text-gray-600 ml-8 mt-1">
                      We guarantee you'll get your deposit back or we'll cover the difference
                    </p>
                  </div>

                  {/* Landlord Requirements */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-3">
                      Special Landlord Requirements (Optional)
                    </label>
                                         <textarea
                       placeholder="Any specific requirements from your landlord or property manager..."
                       value={formData.landlordRequirements || ''}
                       onChange={(e) => setFormData({ ...formData, landlordRequirements: e.target.value })}
                       className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md"
                       rows={3}
                     />
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                                         <Button 
                       onClick={() => setCurrentStep(3)}
                       disabled={!isStepValid(2)}
                       className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                     >
                      Next: Add-ons
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Enhance your service</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((service) => {
                      const IconComponent = service.icon;
                      const isSelected = (formData.addOns || []).includes(service.id);
                      
                      return (
                                                 <motion.button
                           key={service.id}
                           onClick={() => handleAddOnToggle(service.id)}
                           whileHover={{ scale: 1.02, y: -2 }}
                           whileTap={{ scale: 0.98 }}
                           className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                             isSelected 
                               ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                               : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                           }`}
                         >
                           <div className="flex items-center justify-between">
                             <div className="flex items-center gap-3">
                               <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                 isSelected 
                                   ? 'bg-emerald-100 text-emerald-800' 
                                   : 'bg-gray-100 text-gray-600'
                               }`}>
                                 <IconComponent className="w-5 h-5" />
                               </div>
                               <div>
                                 <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                                 <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-xs inline-block">${service.price}</p>
                               </div>
                             </div>
                             {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Price Summary */}
                                     <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6 shadow-sm">
                     <div className="flex justify-between items-center">
                       <span className="text-gray-900 font-semibold">Estimated Total:</span>
                       <span className="text-2xl font-bold text-emerald-800">${calculateTotalPrice()}</span>
                     </div>
                   </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                                         <Button 
                       onClick={() => setCurrentStep(4)}
                       className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                     >
                      Next: Contact & Schedule
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Schedule */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact & Scheduling</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                                                 className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                           validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                         }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Please enter a complete address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>

                    <div>
                      <input 
                        type="text" 
                        placeholder="City" 
                        value={formData.city || ''} 
                        onChange={(e) => handleInputChange('city', e.target.value)} 
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.city ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.city && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                      )}
                    </div>
                    
                    <div>
                      <input 
                        type="text" 
                        placeholder="ZIP Code" 
                        value={formData.zipCode || ''} 
                        onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.zipCode && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                      )}
                    </div>
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special Instructions" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Move-Out Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          const serviceType = 'residential_move';
          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            cleaningType: 'move',
            frequency: 'one-time',
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernMoveOutForm; 