import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Building, Shield, ShieldCheck, Zap,
  Mail, Phone, MapPin, Wind, Star,
  Heart
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';

import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  sanitizationLevel: string;
  healthConcerns: string[];
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernSanitizationForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    sanitizationLevel: '',
    healthConcerns: [],
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('sanitizationFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('sanitizationFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property & Level' },
    { id: 2, name: 'Health Concerns' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: 'Full home treatment',
      multiplier: 1.0
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Compact space',
      multiplier: 0.8
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Building, 
      description: 'Multi-level home',
      multiplier: 0.9
    },
    { 
      id: 'office', 
      name: 'Small Office', 
      icon: Building2, 
      description: 'Commercial workspace',
      multiplier: 1.1
    }
  ];

  const sanitizationLevels = {
    house: [
      { 
        id: 'basic', 
        name: 'Basic Sanitization', 
        icon: ShieldCheck,
        description: 'High-touch surface treatment',
        details: 'Door handles, counters, switches - 2-3 hours',
        basePrice: 149
      },
      { 
        id: 'standard', 
        name: 'Standard Service', 
        icon: Shield,
        description: 'Comprehensive surface treatment',
        details: 'All surfaces, bathrooms, kitchen - 3-4 hours',
        basePrice: 219,
        popular: true
      },
      { 
        id: 'deep', 
        name: 'Deep Sanitization', 
        icon: Zap,
        description: 'Full space disinfection',
        details: 'Complete home treatment - 4-5 hours',
        basePrice: 299
      },
      { 
        id: 'medical-grade', 
        name: 'Medical Grade', 
        icon: Sparkles,
        description: 'Hospital-level disinfection',
        details: 'Maximum protection protocol - 5-6 hours',
        basePrice: 399
      }
    ],
    apartment: [
      { 
        id: 'basic', 
        name: 'Basic Treatment', 
        icon: ShieldCheck,
        description: 'Essential surface disinfection',
        details: 'High-touch areas only - 1-1.5 hours',
        basePrice: 99
      },
      { 
        id: 'standard', 
        name: 'Complete Service', 
        icon: Shield,
        description: 'Full apartment sanitization',
        details: 'All living spaces - 1.5-2 hours',
        basePrice: 169,
        popular: true
      },
      { 
        id: 'medical-grade', 
        name: 'Medical Grade', 
        icon: Sparkles,
        description: 'Maximum protection level',
        details: 'Hospital-level treatment - 2-2.5 hours',
        basePrice: 249
      }
    ],
    townhouse: [
      { 
        id: 'standard', 
        name: 'Standard Service', 
        icon: Shield,
        description: 'Multi-level treatment',
        details: 'All floors and common areas - 2-3 hours',
        basePrice: 199
      },
      { 
        id: 'deep', 
        name: 'Deep Sanitization', 
        icon: Zap,
        description: 'Comprehensive disinfection',
        details: 'Complete townhouse protocol - 3-4 hours',
        basePrice: 279,
        popular: true
      }
    ],
    office: [
      { 
        id: 'standard', 
        name: 'Office Standard', 
        icon: Shield,
        description: 'Workspace disinfection',
        details: 'Desks, common areas, restrooms - 1-2 hours',
        basePrice: 179
      },
      { 
        id: 'medical-grade', 
        name: 'Medical Grade', 
        icon: Sparkles,
        description: 'Maximum workplace protection',
        details: 'Full office sterilization - 2-3 hours',
        basePrice: 299,
        popular: true
      }
    ]
  };

  const healthConcernOptions = [
    { id: 'covid-19', name: 'COVID-19 Exposure', icon: Shield },
    { id: 'flu-illness', name: 'Flu/Illness Recovery', icon: Heart },
    { id: 'allergies', name: 'Allergies & Asthma', icon: Wind },
    { id: 'immunocompromised', name: 'Immunocompromised Family', icon: ShieldCheck },
    { id: 'elderly-care', name: 'Elderly Care Facility', icon: Star },
    { id: 'childcare', name: 'Childcare Environment', icon: Heart },
    { id: 'pet-odors', name: 'Pet Allergens & Odors', icon: Heart },
    { id: 'mold-mildew', name: 'Mold & Mildew Issues', icon: Zap }
  ];

  const addOnServices = [
    { id: 'air-purification', name: 'Air Purification', price: 65, icon: Wind },
    { id: 'antimicrobial-coating', name: 'Antimicrobial Coating', price: 89, icon: Shield },
    { id: 'follow-up', name: 'Follow-up Treatment', price: 45, icon: Sparkles },
    { id: 'certification', name: 'Sanitization Certificate', price: 25, icon: ShieldCheck },
    { id: 'uv-treatment', name: 'UV Light Treatment', price: 79, icon: Zap },
    { id: 'ozone-treatment', name: 'Ozone Air Treatment', price: 99, icon: Wind },
    { id: 'hospital-grade', name: 'Hospital-Grade Chemicals', price: 39, icon: Star },
    { id: 'protective-equipment', name: 'Personal Protective Equipment', price: 19, icon: ShieldCheck }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 11AM)' },
    { id: 'midday', name: 'Midday (11AM - 2PM)' },
    { id: 'afternoon', name: 'Afternoon (2PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 8PM)' }
  ];

  // Get sanitization levels based on selected property type
  const getSanitizationLevels = () => {
    if (!formData.propertyType) return [];
    return sanitizationLevels[formData.propertyType as keyof typeof sanitizationLevels] || [];
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code' : '';
      }
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Validate field on change
    const error = validateField(field, value);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const calculateTotalPrice = (): number => {
    const levels = getSanitizationLevels();
    const selectedLevel = levels.find(level => level.id === formData.sanitizationLevel);
    const selectedProperty = propertyTypes.find(type => type.id === formData.propertyType);
    
    let basePrice = selectedLevel?.basePrice || 169;
    basePrice *= selectedProperty?.multiplier || 1.0;
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      const addOn = addOnServices.find(service => service.id === addOnId);
      return total + (addOn?.price || 0);
    }, 0);

    return Math.round(basePrice + addOnTotal);
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.sanitizationLevel);
      case 2:
        return true; // Health concerns are optional
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.firstName && 
          formData.lastName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode && 
          formData.preferredDate && 
          formData.preferredTime &&
          !Object.keys(validationErrors).length
        );
      default:
        return false;
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use 'sanitization' service type
      const serviceType = 'sanitization';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        cleaningType: 'sanitization',
        frequency: 'one-time', // Sanitization is typically one-time
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('sanitizationBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      const serviceType = 'sanitization';

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: 'one-time',
          cleaningType: 'sanitization',
          sanitizationLevel: formData.sanitizationLevel,
          healthConcerns: formData.healthConcerns || [],
          addOns: formData.addOns || [],
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_sanitization_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save sanitization booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Sanitization booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('sanitizationBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `SAN-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Sanitization',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Sanitization',
            serviceType: 'Sanitization',
            status: 'confirmed',
            message: `Your sanitization service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `SAN-${Date.now()}`,
            confirmationNumber: `SAN-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Sanitization',
          bookingDetails: {
            id: `SAN-${Date.now()}`,
            type: 'Sanitization',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  const handleHealthConcernToggle = (concernId: string) => {
    const currentConcerns = formData.healthConcerns || [];
    if (currentConcerns.includes(concernId)) {
      setFormData({
        ...formData,
        healthConcerns: currentConcerns.filter(id => id !== concernId)
      });
    } else {
      setFormData({
        ...formData,
        healthConcerns: [...currentConcerns, concernId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Professional Sanitization Service
            </h1>
            <p className="text-gray-600">Hospital-grade disinfection for your safety and peace of mind.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} style={{ color: 'white' }} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property & Level */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Property Type & Sanitization Level</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <motion.button
                            key={type.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, propertyType: type.id, sanitizationLevel: '' })}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              formData.propertyType === type.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.propertyType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Sanitization Level */}
                  {formData.propertyType && (
                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-900 mb-4">Sanitization Level</label>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {getSanitizationLevels().map((level) => {
                          const IconComponent = level.icon;
                          return (
                            <motion.button
                              key={level.id}
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => setFormData({ ...formData, sanitizationLevel: level.id })}
                              className={`p-4 rounded-xl border-2 text-left relative transition-all duration-200 ${
                                formData.sanitizationLevel === level.id 
                                  ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                  : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                              }`}
                            >
                              {level.popular && (
                                <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                  Popular
                                </div>
                              )}
                              <div className="flex items-center gap-4">
                                <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                  formData.sanitizationLevel === level.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <div>
                                  <h3 className="font-semibold text-gray-900">{level.name}</h3>
                                  <p className="text-sm text-gray-600">{level.description}</p>
                                  <p className="text-xs text-gray-500">{level.details}</p>
                                  <p className="text-sm text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">Starting at ${level.basePrice}</p>
                                </div>
                              </div>
                            </motion.button>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => window.history.back()}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(2)}
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Health Concerns
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Health Concerns */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Health Concerns & Focus Areas</h2>
                  
                  {/* Health Concerns */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Select Any Health Concerns (Optional)</label>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                      {healthConcernOptions.map((concern) => {
                        const IconComponent = concern.icon;
                        const isSelected = (formData.healthConcerns || []).includes(concern.id);
                        
                        return (
                          <motion.button
                            key={concern.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleHealthConcernToggle(concern.id)}
                            className={`p-3 rounded-xl border-2 text-center transition-all duration-200 ${
                              isSelected 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <div className={`p-1 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <span className="text-sm text-gray-900 font-medium">{concern.name}</span>
                              {isSelected && <CheckCircle className="w-4 h-4 text-emerald-600" />}
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Info Box */}
                  <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-lg bg-emerald-100">
                        <Shield className="w-5 h-5 text-emerald-800" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Our Sanitization Process</h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>• EPA-approved disinfectants effective against viruses & bacteria</li>
                          <li>• Electrostatic spraying for even coverage on all surfaces</li>
                          <li>• Focus on high-touch areas and health concern zones</li>
                          <li>• Professional-grade equipment and certified technicians</li>
                          <li>• Safe for family, pets, and sensitive individuals</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)}
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Add-ons
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Enhance your sanitization</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((service) => {
                      const IconComponent = service.icon;
                      const isSelected = (formData.addOns || []).includes(service.id);
                      
                      return (
                        <motion.button
                          key={service.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleAddOnToggle(service.id)}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            isSelected 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-xs inline-block">${service.price}</p>
                              </div>
                            </div>
                            {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Price Summary */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-900 font-semibold">Estimated Total:</span>
                      <span className="text-2xl font-bold text-emerald-800">${calculateTotalPrice()}</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next: Contact & Schedule
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Schedule */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact & Scheduling</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Property Address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={(e) => handleInputChange('city', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.city ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.city && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                    )}
                    
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.zipCode && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                    )}
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special instructions, health considerations, or access details" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Professional Sanitization Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          const serviceType = 'sanitization';
          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            cleaningType: 'sanitization',
            frequency: 'one-time',
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernSanitizationForm;