import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Droplets, Shield, Wrench, Waves,
  Mail, Phone, MapPin, Star,
  Zap, Eye, Heart
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';



interface FormData {
  propertyType: string;
  serviceFrequency: string;
  poolSize: string;
  poolType: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernPoolCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    serviceFrequency: '',
    poolSize: '',
    poolType: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('poolCleaningFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('poolCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property & Service' },
    { id: 2, name: 'Pool Details' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'residential', 
      name: 'Residential Pool', 
      icon: Home, 
      description: 'Backyard swimming pool',
      multiplier: 1.0
    },
    { 
      id: 'apartment', 
      name: 'Apartment/Condo Pool', 
      icon: Building2, 
      description: 'Shared community pool',
      multiplier: 1.3
    },
    { 
      id: 'spa', 
      name: 'Spa/Hot Tub', 
      icon: Droplets, 
      description: 'Hot tub or spa',
      multiplier: 0.8
    }
  ];

  const serviceFrequencies = {
    residential: [
      { 
        id: 'basic', 
        name: 'One-Time Cleaning', 
        icon: Sparkles,
        description: 'One-time deep clean',
        details: 'Skimming, vacuuming, basic chemical check - 2-3 hours',
        basePrice: 129
      },
      { 
        id: 'weekly', 
        name: 'Weekly Service', 
        icon: Waves,
        description: 'Premium maintenance plan',
        details: 'Complete weekly pool care - 1-2 hours/week',
        basePrice: 89,
        popular: true
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Service', 
        icon: Shield,
        description: 'Every two weeks',
        details: 'Regular maintenance schedule - 1.5-2 hours/visit',
        basePrice: 109
      },
      { 
        id: 'monthly', 
        name: 'Monthly Service', 
        icon: Wrench,
        description: 'Minimal maintenance plan',
        details: 'Monthly pool maintenance - 2-3 hours/visit',
        basePrice: 149
      }
    ],
    apartment: [
      { 
        id: 'weekly', 
        name: 'Weekly Maintenance', 
        icon: Waves,
        description: 'Community pool service',
        details: 'High-traffic pool maintenance - 2-3 hours/week',
        basePrice: 149,
        popular: true
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Service', 
        icon: Shield,
        description: 'Every two weeks',
        details: 'Regular community maintenance - 2.5-3 hours/visit',
        basePrice: 199
      },
      { 
        id: 'monthly', 
        name: 'Monthly Deep Clean', 
        icon: Wrench,
        description: 'Comprehensive monthly service',
        details: 'Full community pool treatment - 3-4 hours/visit',
        basePrice: 279
      }
    ],
    spa: [
      { 
        id: 'weekly', 
        name: 'Weekly Spa Service', 
        icon: Waves,
        description: 'Regular spa maintenance',
        details: 'Complete spa care package - 0.5-1 hour/week',
        basePrice: 79,
        popular: true
      },
      { 
        id: 'bi-weekly', 
        name: 'Bi-Weekly Care', 
        icon: Shield,
        description: 'Every two weeks',
        details: 'Extended spa maintenance - 1-1.5 hours/visit',
        basePrice: 89
      },
      { 
        id: 'monthly', 
        name: 'Monthly Service', 
        icon: Wrench,
        description: 'Basic spa maintenance',
        details: 'Essential monthly spa care - 1-1.5 hours/visit',
        basePrice: 99
      }
    ]
  };

  const poolSizes = [
    { id: 'small', name: 'Small (up to 10,000 gallons)', icon: Droplets },
    { id: 'medium', name: 'Medium (10,000-20,000 gallons)', icon: Waves },
    { id: 'large', name: 'Large (20,000-30,000 gallons)', icon: Shield },
    { id: 'extra-large', name: 'Extra Large (30,000+ gallons)', icon: Star }
  ];

  const poolTypes = [
    { id: 'chlorine', name: 'Chlorine Pool', icon: Sparkles },
    { id: 'saltwater', name: 'Saltwater Pool', icon: Waves },
    { id: 'natural', name: 'Natural Pool', icon: Heart },
    { id: 'vinyl', name: 'Vinyl Liner Pool', icon: Shield },
    { id: 'fiberglass', name: 'Fiberglass Pool', icon: Eye },
    { id: 'concrete', name: 'Concrete Pool', icon: Wrench }
  ];

  const addOnServices = [
    { id: 'chemical-balancing', name: 'Chemical Balancing', price: 35, icon: Sparkles },
    { id: 'filter-cleaning', name: 'Filter Cleaning', price: 25, icon: Shield },
    { id: 'equipment-check', name: 'Equipment Inspection', price: 45, icon: Wrench },
    { id: 'algae-treatment', name: 'Algae Treatment', price: 55, icon: Zap },
    { id: 'tile-cleaning', name: 'Tile Cleaning', price: 65, icon: Star },
    { id: 'pool-vacuum', name: 'Deep Vacuum Service', price: 40, icon: Waves },
    { id: 'water-testing', name: 'Complete Water Testing', price: 20, icon: Eye },
    { id: 'shock-treatment', name: 'Shock Treatment', price: 30, icon: Heart }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 11AM)' },
    { id: 'midday', name: 'Midday (11AM - 2PM)' },
    { id: 'afternoon', name: 'Afternoon (2PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 7PM)' }
  ];

  // Get service frequencies based on selected property type
  const getServiceFrequencies = () => {
    if (!formData.propertyType) return [];
    return serviceFrequencies[formData.propertyType as keyof typeof serviceFrequencies] || [];
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code' : '';
      }
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Validate field on change
    const error = validateField(field, value);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const calculateTotalPrice = (): number => {
    const frequencies = getServiceFrequencies();
    const selectedFrequency = frequencies.find(freq => freq.id === formData.serviceFrequency);
    const selectedProperty = propertyTypes.find(type => type.id === formData.propertyType);
    
    let basePrice = selectedFrequency?.basePrice || 89;
    basePrice *= selectedProperty?.multiplier || 1.0;
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      const addOn = addOnServices.find(service => service.id === addOnId);
      return total + (addOn?.price || 0);
    }, 0);

    return Math.round(basePrice + addOnTotal);
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.serviceFrequency);
      case 2:
        return !!(formData.poolSize && formData.poolType);
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.firstName && 
          formData.lastName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode && 
          formData.preferredDate && 
          formData.preferredTime &&
          !Object.keys(validationErrors).length
        );
      default:
        return false;
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use 'pool-cleaning' service type
      const serviceType = 'pool-cleaning';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        cleaningType: 'pool',
        frequency: formData.serviceFrequency === 'basic' ? 'one-time' : formData.serviceFrequency,
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('poolBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      const serviceType = 'pool-cleaning';

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: formData.serviceFrequency === 'basic' ? 'one-time' : formData.serviceFrequency,
          cleaningType: 'pool',
          serviceFrequency: formData.serviceFrequency,
          poolSize: formData.poolSize,
          poolType: formData.poolType,
          addOns: formData.addOns || [],
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_pool_cleaning_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save pool cleaning booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Pool cleaning booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('poolBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `POOL-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Pool Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Pool Cleaning',
            serviceType: 'Pool Cleaning',
            status: 'confirmed',
            message: `Your pool cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `POOL-${Date.now()}`,
            confirmationNumber: `POOL-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Pool Cleaning',
          bookingDetails: {
            id: `POOL-${Date.now()}`,
            type: 'Pool Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Professional Pool Cleaning
            </h1>
            <p className="text-gray-600">Crystal clear water and pristine pool maintenance for your home.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} style={{ color: 'white' }} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property & Service */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Property Type & Service Frequency</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <motion.button
                            key={type.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, propertyType: type.id, serviceFrequency: '' })}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              formData.propertyType === type.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.propertyType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Service Frequency */}
                  {formData.propertyType && (
                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-900 mb-4">Service Frequency</label>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        {getServiceFrequencies().map((freq) => {
                          const IconComponent = freq.icon;
                          return (
                            <motion.button
                              key={freq.id}
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => setFormData({ ...formData, serviceFrequency: freq.id })}
                              className={`p-4 rounded-xl border-2 text-left relative transition-all duration-200 ${
                                formData.serviceFrequency === freq.id 
                                  ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                  : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                              }`}
                            >
                              {freq.popular && (
                                <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                  Popular
                                </div>
                              )}
                              <div className="flex items-center gap-4">
                                <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                  formData.serviceFrequency === freq.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-6 h-6" />
                                </div>
                                <div>
                                  <h3 className="font-semibold text-gray-900">{freq.name}</h3>
                                  <p className="text-sm text-gray-600">{freq.description}</p>
                                  <p className="text-xs text-gray-500">{freq.details}</p>
                                  <p className="text-sm text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">Starting at ${freq.basePrice}</p>
                                </div>
                              </div>
                            </motion.button>
                          );
                        })}
                      </div>
                    </div>
                  )}
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => window.history.back()}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(2)}
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Pool Details
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Pool Details */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Pool Size & Type</h2>
                  
                  {/* Pool Size */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Pool Size</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {poolSizes.map((size) => {
                        const IconComponent = size.icon;
                        const isSelected = formData.poolSize === size.id;
                        
                        return (
                          <motion.button
                            key={size.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, poolSize: size.id })}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              isSelected 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`p-1 rounded-lg transition-colors duration-200 ${
                                  isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                                }`}>
                                  <IconComponent className="w-5 h-5" />
                                </div>
                                <span className="text-gray-900 font-medium">{size.name}</span>
                              </div>
                              {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Pool Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Pool Type</label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                      {poolTypes.map((type) => {
                        const IconComponent = type.icon;
                        const isSelected = formData.poolType === type.id;
                        
                        return (
                          <motion.button
                            key={type.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, poolType: type.id })}
                            className={`p-3 rounded-xl border-2 text-center transition-all duration-200 ${
                              isSelected 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <div className={`p-1 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <span className="text-sm text-gray-900 font-medium">{type.name}</span>
                              {isSelected && <CheckCircle className="w-4 h-4 text-emerald-600" />}
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Pool Care Info Box */}
                  <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-lg bg-emerald-100">
                        <Waves className="w-5 h-5 text-emerald-800" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Professional Pool Care</h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>• Skimming and debris removal from surface and bottom</li>
                          <li>• Brushing walls, steps, and tile line</li>
                          <li>• Vacuuming and water circulation check</li>
                          <li>• Chemical testing and basic balancing</li>
                          <li>• Equipment inspection and maintenance</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)}
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Add-ons
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Enhanced Pool Services</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((service) => {
                      const IconComponent = service.icon;
                      const isSelected = (formData.addOns || []).includes(service.id);
                      
                      return (
                        <motion.button
                          key={service.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleAddOnToggle(service.id)}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            isSelected 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-xs inline-block">${service.price}</p>
                              </div>
                            </div>
                            {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Price Summary */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-900 font-semibold">Estimated Total:</span>
                      <span className="text-2xl font-bold text-emerald-800">${calculateTotalPrice()}</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next: Contact & Schedule
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Schedule */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact & Scheduling</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Property Address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={(e) => handleInputChange('city', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.city ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.city && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                    )}
                    
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.zipCode && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                    )}
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special instructions, pool access details, or specific concerns" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Professional Pool Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          const serviceType = 'pool-cleaning';
          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            cleaningType: 'pool',
            frequency: formData.serviceFrequency === 'basic' ? 'one-time' : formData.serviceFrequency,
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernPoolCleaningForm;