import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Warehouse, HardHat, 
  Brush, Sofa, GlassWater, Briefcase, Trash2, Waves, Droplets, Flame
} from 'lucide-react';

const services = [
  {
    id: 'office',
    icon: Building,
    title: 'Office Cleaning',
    description: 'Daily, weekly, or monthly cleaning for a pristine office environment.',
    price: 'Custom',
    popular: true,
    features: ['Workstation & common area sanitization', 'Restroom cleaning', 'Trash & recycling', 'Floor care'],
    color: 'from-blue-500 to-blue-600',
    glowColor: 'rgba(59, 130, 246, 0.3)'
  },
  {
    id: 'industrial',
    icon: Warehouse,
    title: 'Industrial & Warehouse',
    description: 'Heavy-duty cleaning for industrial and warehouse facilities.',
    price: 'Custom',
    features: ['High-dusting', 'Machinery cleaning', 'Floor scrubbing', 'Degreasing services'],
    color: 'from-gray-500 to-gray-600',
    glowColor: 'rgba(107, 114, 128, 0.3)'
  },
  {
    id: 'post-construction',
    icon: HardHat,
    title: 'Post-Construction',
    description: 'Final cleaning after construction or renovation projects.',
    price: 'Custom',
    features: ['Debris removal', 'Fine dust cleaning', 'Window & surface polishing'],
    color: 'from-orange-400 to-orange-600',
    glowColor: 'rgba(251, 146, 60, 0.4)',
    path: '/commercial/post-construction'
  },
  {
    id: 'floor-care',
    icon: Brush,
    title: 'Specialized Floor Care',
    description: 'Stripping, waxing, buffing, and polishing for all floor types.',
    price: 'Custom',
    features: ['Vinyl, tile, hardwood', 'Deep scrub & recoat', 'High-speed burnishing'],
    color: 'from-green-500 to-green-600',
    glowColor: 'rgba(34, 197, 94, 0.3)'
  },
  {
    id: 'carpet-upholstery',
    icon: Sofa,
    title: 'Carpet & Upholstery',
    description: 'Commercial-grade deep cleaning for carpets and furniture.',
    price: 'Custom',
    features: ['Hot water extraction', 'Stain & spot treatment', 'Odor control'],
    color: 'from-indigo-500 to-indigo-600',
    glowColor: 'rgba(99, 102, 241, 0.3)'
  },
  {
    id: 'window-cleaning',
    icon: GlassWater,
    title: 'Window Cleaning',
    description: 'High-rise and ground-level window cleaning services.',
    price: 'Custom',
    features: ['Interior & exterior', 'Skylights & atriums', 'Hard water stain removal'],
    color: 'from-cyan-500 to-cyan-600',
    glowColor: 'rgba(6, 182, 212, 0.3)'
  },
  {
    id: 'corporate',
    icon: Briefcase,
    title: 'Corporate Services',
    description: 'Tailored cleaning solutions for corporate headquarters and offices.',
    price: 'Custom',
    features: ['Executive suite cleaning', 'Conference room maintenance', 'Reception area care', 'Document security'],
    color: 'from-purple-500 to-purple-600',
    glowColor: 'rgba(147, 51, 234, 0.3)'
  },
  {
    id: 'waste-management',
    icon: Trash2,
    title: 'Waste Management',
    description: 'Efficient and compliant waste and recycling services.',
    price: 'Custom',
    features: ['Waste collection & disposal', 'Recycling programs', 'Hazardous waste handling', 'Compliance reporting'],
    color: 'from-red-500 to-red-600',
    glowColor: 'rgba(239, 68, 68, 0.3)'
  },
  {
    id: 'pressure-washing',
    icon: Waves,
    title: 'Pressure Washing',
    description: 'High-power washing for building exteriors, parking lots, and walkways.',
    price: 'Custom',
    features: ['Building exterior cleaning', 'Parking lot maintenance', 'Walkway restoration', 'Graffiti removal'],
    color: 'from-teal-500 to-teal-600',
    glowColor: 'rgba(20, 184, 166, 0.3)'
  },
  {
    id: 'sanitization',
    icon: Droplets,
    title: 'Sanitization Services',
    description: 'Advanced disinfection for a healthier commercial environment.',
    price: 'Custom',
    features: ['EPA-approved disinfectants', 'High-touch surface sanitization', 'Air quality improvement', 'Health compliance'],
    color: 'from-emerald-500 to-emerald-600',
    glowColor: 'rgba(16, 185, 129, 0.3)'
  },
  {
    id: 'kitchen',
    icon: Flame,
    title: 'Commercial Kitchen',
    description: 'Deep cleaning and degreasing for restaurant and commercial kitchens.',
    price: 'Custom',
    features: ['Hood & vent cleaning', 'Grease trap maintenance', 'Equipment degreasing', 'Health code compliance'],
    color: 'from-amber-500 to-amber-600',
    glowColor: 'rgba(245, 158, 11, 0.3)'
  },
];

export function ServicesGrid() {
  const navigate = useNavigate();

  const handleServiceClick = (serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    if (service && 'path' in service && service.path) {
      navigate(service.path);
    } else {
      navigate(`/commercial/${serviceId}`);
    }
  };

  return (
    <section className="py-24 sm:py-32" id="commercial-services">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Solutions for Every Business</h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            We provide scalable cleaning solutions for businesses of all sizes and industries.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                whileHover={{ scale: 1.02, y: -4 }}
                whileTap={{ scale: 0.98 }}
                className="relative p-8 rounded-2xl border border-gray-200 h-full flex flex-col group cursor-pointer bg-white shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => handleServiceClick(service.id)}
              >
                {service.popular && (
                  <div className="absolute top-0 right-8 -mt-4">
                    <div className="px-4 py-1.5 rounded-full text-sm font-semibold text-white bg-emerald-600 shadow-lg">
                      Popular
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl mb-6 border border-emerald-200 shadow-sm">
                  <Icon className="w-6 h-6 text-emerald-800" />
                </div>

                <div className="flex-grow">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">{service.description}</p>
                </div>
                
                <div className="mt-auto flex justify-between items-center">
                  <span className="text-lg font-semibold text-emerald-800 bg-emerald-100 px-3 py-1 rounded-lg">{service.price}</span>
                  <div className="text-gray-600 group-hover:text-emerald-800 transition-colors">
                    <span className="mr-2 font-medium">Get Quote</span>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
} 
