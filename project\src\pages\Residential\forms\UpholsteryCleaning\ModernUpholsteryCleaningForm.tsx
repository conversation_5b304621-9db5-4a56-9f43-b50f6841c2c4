import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Sofa, Armchair, Layers, ShoppingBag,
  Mail, Phone, MapPin, Gift, Zap, Shield, Star,
  Wind, SprayCan, Clock, Palette
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  numberOfPieces: string;
  furnitureTypes: string[];
  materialType: string;
  cleaningIntensity: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernUpholsteryCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    numberOfPieces: '',
    furnitureTypes: [],
    materialType: '',
    cleaningIntensity: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('upholsteryCleaningFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('upholsteryCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property & Pieces' },
    { id: 2, name: 'Furniture Details' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units',
      multiplier: 1.0
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes',
      multiplier: 1.1
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Building2, 
      description: 'Multi-level attached',
      multiplier: 1.05
    },
    { 
      id: 'office', 
      name: 'Office/Commercial', 
      icon: Building2, 
      description: 'Business furniture',
      multiplier: 1.15
    }
  ];

  const pieceOptions = [
    { 
      id: '1-2', 
      name: '1-2 Pieces', 
      icon: Armchair,
      description: 'Single chair or loveseat',
      details: '1-1.5 hours service',
      basePrice: 89
    },
    { 
      id: '3-4', 
      name: '3-4 Pieces', 
      icon: Sofa,
      description: 'Sofa + chair combo',
      details: '1.5-2 hours service',
      basePrice: 149,
      popular: true
    },
    { 
      id: '5-6', 
      name: '5-6 Pieces', 
      icon: Layers,
      description: 'Full living room set',
      details: '2-2.5 hours service',
      basePrice: 219
    },
    { 
      id: '7+', 
      name: '7+ Pieces', 
      icon: ShoppingBag,
      description: 'Multiple rooms',
      details: '2.5-3 hours service',
      basePrice: 289
    }
  ];

  const furnitureTypeOptions = [
    { id: 'sofa', name: 'Sofa/Couch', icon: Sofa },
    { id: 'loveseat', name: 'Loveseat', icon: Armchair },
    { id: 'chair', name: 'Chair', icon: Armchair },
    { id: 'sectional', name: 'Sectional', icon: Layers },
    { id: 'ottoman', name: 'Ottoman', icon: Gift },
    { id: 'recliner', name: 'Recliner', icon: Armchair },
    { id: 'dining-chair', name: 'Dining Chair', icon: Armchair },
    { id: 'mattress', name: 'Mattress', icon: Layers }
  ];

  const materialTypes = [
    { 
      id: 'fabric', 
      name: 'Fabric/Cotton', 
      description: 'Standard woven materials',
      details: 'Most common upholstery',
      priceMultiplier: 1.0
    },
    { 
      id: 'microfiber', 
      name: 'Microfiber', 
      description: 'Synthetic microfiber',
      details: 'Easy care material',
      priceMultiplier: 1.0
    },
    { 
      id: 'leather', 
      name: 'Leather', 
      description: 'Genuine leather',
      details: 'Requires special care (+15%)',
      priceMultiplier: 1.15
    },
    { 
      id: 'suede', 
      name: 'Suede/Velvet', 
      description: 'Delicate nap materials',
      details: 'Premium care required (+20%)',
      priceMultiplier: 1.2
    }
  ];

  const cleaningIntensityOptions = [
    { 
      id: 'standard', 
      name: 'Standard Clean', 
      price: 0, 
      description: 'Regular maintenance cleaning',
      features: ['Surface cleaning', 'Spot treatment', 'Light vacuuming', 'Quick drying']
    },
    { 
      id: 'deep', 
      name: 'Deep Clean', 
      price: 25, 
      description: 'Thorough restoration cleaning',
      features: ['Pre-treatment', 'Deep extraction', 'Stain treatment', 'Fabric conditioning', 'Extended drying']
    },
    { 
      id: 'premium', 
      name: 'Premium Restore', 
      price: 45, 
      description: 'Heavy soiling & stain removal',
      features: ['Everything in Deep Clean', 'Multiple passes', 'Heavy stain treatment', 'Sanitization', 'Fabric protection']
    }
  ];

  const addOnServices = [
    { id: 'fabric-protection', name: 'Fabric Protection', price: 49, icon: Shield },
    { id: 'scotchgard', name: 'Scotchgard Treatment', price: 69, icon: Star },
    { id: 'stain-treatment', name: 'Advanced Stain Treatment', price: 39, icon: SprayCan },
    { id: 'deodorizing', name: 'Deep Deodorizing', price: 29, icon: Wind },
    { id: 'antimicrobial', name: 'Antimicrobial Treatment', price: 59, icon: Zap },
    { id: 'pet-treatment', name: 'Pet Odor Treatment', price: 49, icon: Sparkles },
    { id: 'color-restoration', name: 'Color Restoration', price: 79, icon: Palette },
    { id: 'rush-service', name: 'Same-Day Rush Service', price: 89, icon: Clock }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code' : '';
      }
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Validate field on change
    const error = validateField(field, value);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const calculateTotalPrice = (): number => {
    const selectedPieces = pieceOptions.find(piece => piece.id === formData.numberOfPieces);
    const selectedProperty = propertyTypes.find(type => type.id === formData.propertyType);
    const selectedMaterial = materialTypes.find(type => type.id === formData.materialType);
    const selectedIntensity = cleaningIntensityOptions.find(intensity => intensity.id === formData.cleaningIntensity);
    
    let basePrice = selectedPieces?.basePrice || 149;
    basePrice *= selectedProperty?.multiplier || 1.0;
    basePrice *= selectedMaterial?.priceMultiplier || 1.0;
    basePrice += selectedIntensity?.price || 0;
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      const addOn = addOnServices.find(service => service.id === addOnId);
      return total + (addOn?.price || 0);
    }, 0);

    return Math.round(basePrice + addOnTotal);
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.numberOfPieces);
      case 2:
        return !!(formData.furnitureTypes && formData.furnitureTypes.length > 0 && formData.materialType && formData.cleaningIntensity);
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.firstName && 
          formData.lastName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode && 
          formData.preferredDate && 
          formData.preferredTime &&
          !Object.keys(validationErrors).length
        );
      default:
        return false;
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use 'upholstery-cleaning' service type
      const serviceType = 'upholstery-cleaning';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        cleaningType: 'upholstery',
        frequency: 'one-time', // Upholstery cleaning is typically one-time
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('upholsteryCleaningBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      const serviceType = 'upholstery-cleaning';

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: 'one-time',
          cleaningType: 'upholstery',
          numberOfPieces: formData.numberOfPieces,
          furnitureTypes: formData.furnitureTypes || [],
          materialType: formData.materialType,
          cleaningIntensity: formData.cleaningIntensity,
          addOns: formData.addOns || [],
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_upholstery_cleaning_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save upholstery cleaning booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Upholstery cleaning booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('upholsteryCleaningBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `UC-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Upholstery Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Upholstery Cleaning',
            serviceType: 'Upholstery Cleaning',
            status: 'confirmed',
            message: `Your upholstery cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `UC-${Date.now()}`,
            confirmationNumber: `UC-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Upholstery Cleaning',
          bookingDetails: {
            id: `UC-${Date.now()}`,
            type: 'Upholstery Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  const handleFurnitureTypeToggle = (furnitureId: string) => {
    const currentTypes = formData.furnitureTypes || [];
    if (currentTypes.includes(furnitureId)) {
      setFormData({
        ...formData,
        furnitureTypes: currentTypes.filter(id => id !== furnitureId)
      });
    } else {
      setFormData({
        ...formData,
        furnitureTypes: [...currentTypes, furnitureId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Professional Upholstery Cleaning
            </h1>
            <p className="text-gray-600">Revitalize your furniture with deep cleaning and fabric restoration.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} style={{ color: 'white' }} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property & Pieces */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Property & Furniture Count</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <motion.button
                            key={type.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, propertyType: type.id })}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              formData.propertyType === type.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.propertyType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Number of Pieces */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Number of Furniture Pieces</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {pieceOptions.map((piece) => {
                        const IconComponent = piece.icon;
                        return (
                          <motion.button
                            key={piece.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, numberOfPieces: piece.id })}
                            className={`p-4 rounded-xl border-2 text-left relative transition-all duration-200 ${
                              formData.numberOfPieces === piece.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            {piece.popular && (
                              <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                Popular
                              </div>
                            )}
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.numberOfPieces === piece.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{piece.name}</h3>
                                <p className="text-sm text-gray-600">{piece.description}</p>
                                <p className="text-xs text-gray-500">{piece.details}</p>
                                <p className="text-sm text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">Starting at ${piece.basePrice}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => window.history.back()}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(2)}
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Furniture Details
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Furniture Details */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Furniture & Material Details</h2>
                  
                  {/* Furniture Types */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Furniture Types (Select all that apply)</label>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
                      {furnitureTypeOptions.map((furniture) => {
                        const IconComponent = furniture.icon;
                        const isSelected = (formData.furnitureTypes || []).includes(furniture.id);
                        
                        return (
                          <motion.button
                            key={furniture.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => handleFurnitureTypeToggle(furniture.id)}
                            className={`p-3 rounded-xl border-2 text-center transition-all duration-200 ${
                              isSelected 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex flex-col items-center gap-2">
                              <div className={`p-1 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <span className="text-sm text-gray-900 font-medium">{furniture.name}</span>
                              {isSelected && <CheckCircle className="w-4 h-4 text-emerald-600" />}
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Material Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Material Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {materialTypes.map((material) => (
                        <motion.button
                          key={material.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, materialType: material.id })}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.materialType === material.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <h3 className="font-semibold text-gray-900">{material.name}</h3>
                          <p className="text-sm text-gray-600">{material.description}</p>
                          <p className="text-xs text-gray-500">{material.details}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Cleaning Intensity */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Cleaning Level</label>
                    <div className="grid grid-cols-1 gap-4">
                      {cleaningIntensityOptions.map((intensity) => (
                        <motion.button
                          key={intensity.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, cleaningIntensity: intensity.id })}
                          className={`p-6 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.cleaningIntensity === intensity.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{intensity.name}</h3>
                              <p className="text-gray-600">{intensity.description}</p>
                              {intensity.price > 0 && (
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">+${intensity.price}</p>
                              )}
                            </div>
                            <div className={`p-2 rounded-lg transition-colors duration-200 ${
                              formData.cleaningIntensity === intensity.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                            }`}>
                              <Sofa className="w-6 h-6" />
                            </div>
                          </div>
                          <ul className="text-sm text-gray-700 space-y-1">
                            {intensity.features.map((feature, index) => (
                              <li key={index} className="flex items-center">
                                <CheckCircle className="w-4 h-4 text-emerald-600 mr-2" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)}
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Add-ons
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Enhance your upholstery cleaning</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((service) => {
                      const IconComponent = service.icon;
                      const isSelected = (formData.addOns || []).includes(service.id);
                      
                      return (
                        <motion.button
                          key={service.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleAddOnToggle(service.id)}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            isSelected 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-xs inline-block">${service.price}</p>
                              </div>
                            </div>
                            {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Price Summary */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-900 font-semibold">Estimated Total:</span>
                      <span className="text-2xl font-bold text-emerald-800">${calculateTotalPrice()}</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next: Contact & Schedule
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Schedule */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact & Scheduling</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Property Address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={(e) => handleInputChange('city', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.city ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.city && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                    )}
                    
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.zipCode && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                    )}
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special instructions, fabric conditions, or stain details" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Professional Upholstery Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          const serviceType = 'upholstery-cleaning';
          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            cleaningType: 'upholstery',
            frequency: 'one-time',
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernUpholsteryCleaningForm; 