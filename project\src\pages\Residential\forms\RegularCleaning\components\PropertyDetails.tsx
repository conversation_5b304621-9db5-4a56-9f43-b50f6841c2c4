import React from 'react';
import { Building2, <PERSON><PERSON><PERSON>, <PERSON>r, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { motion } from 'framer-motion';

interface PropertyDetailsData {
  propertyType: string;
  squareFootage: number;
  bedrooms: number;
  bathrooms: number;
  floors: number;
  pets: boolean;
  propertyAddress: string;
}

interface PropertyDetailsProps {
  details: PropertyDetailsData;
  onChange: (details: PropertyDetailsData) => void;
}

export function PropertyDetails({ details, onChange }: PropertyDetailsProps) {
  const propertyTypes = [
    'House',
    'Apartment',
    'Condo',
    'Townhouse',
    'Mobile Home',
    'Other'
  ];

  return (
    <div className="space-y-8">
      <div className="flex items-center space-x-4 mb-10">
        <div className="p-3 rounded-2xl bg-emerald-100 border border-emerald-200">
          <Building2 
            className="w-6 h-6" 
            color="#10b981"
            fill="none"
            stroke="#10b981"
            strokeWidth="2"
          />
        </div>
        <div>
          <h3 className="text-2xl font-bold text-gray-900">Property Details</h3>
          <p className="text-gray-700 text-lg">Tell us about your home</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Property Type <span className="text-red-500">*</span>
          </label>
          <select
            value={details.propertyType}
            onChange={(e) => onChange({ ...details, propertyType: e.target.value })}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
            required
          >
            <option value="">Select property type</option>
            {propertyTypes.map((type) => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Square Footage <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <Ruler className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.squareFootage || ''}
              onChange={(e) => onChange({ ...details, squareFootage: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              placeholder="Approximate square footage"
              min="1"
              required
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Bedrooms <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            value={details.bedrooms}
            onChange={(e) => onChange({ ...details, bedrooms: Number(e.target.value) })}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
            min="0"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Bathrooms <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            value={details.bathrooms}
            onChange={(e) => onChange({ ...details, bathrooms: Number(e.target.value) })}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200"
            min="0"
            step="0.5"
            required
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Floors <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <DoorOpen className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="number"
              value={details.floors}
              onChange={(e) => onChange({ ...details, floors: Number(e.target.value) })}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
              min="1"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            Pets
          </label>
          <label className="flex items-center p-3 border border-gray-300 rounded-xl cursor-pointer hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200">
            <input
              type="checkbox"
              checked={details.pets}
              onChange={(e) => onChange({ ...details, pets: e.target.checked })}
              className="sr-only"
            />
            <Dog 
              className="w-5 h-5 mr-2" 
              color={details.pets ? '#10b981' : '#9ca3af'}
              fill="none"
              stroke={details.pets ? '#10b981' : '#9ca3af'}
              strokeWidth="2"
            />
            <span className="text-gray-700 font-medium">Has Pets</span>
          </label>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Property Address <span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={details.propertyAddress}
            onChange={(e) => onChange({ ...details, propertyAddress: e.target.value })}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brand-500 focus:border-transparent"
            placeholder="Enter your complete address"
            required
          />
        </div>
      </div>
    </div>
  );
}
