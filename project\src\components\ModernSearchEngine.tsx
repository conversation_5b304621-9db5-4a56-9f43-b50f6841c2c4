import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { advancedRank } from '../lib/search/advancedSearch';
import { Search, MapPin, ArrowRight, TrendingUp, Zap } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// Advanced Search Algorithm Implementation
interface SearchResult {
  id: string;
  text: string;
  category: 'service' | 'location' | 'trending' | 'recent';
  score: number;
  keywords: string[];
  popularity: number;
  lastSearched?: Date;
}



// Fuzzy search implementation using Levenshtein distance - currently unused
/*
const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));
  
  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }
  
  return matrix[str2.length][str1.length];
};
*/

// Advanced search scoring algorithm
// Unused function - can be removed if not needed elsewhere
/*
const calculateSearchScore = (query: string, result: SearchResult): number => {
  if (!query.trim()) return result.popularity;
  
  const queryLower = query.toLowerCase();
  const textLower = result.text.toLowerCase();
  
  // Exact match bonus
  if (textLower === queryLower) return 100 + result.popularity;
  
  // Starts with bonus
  if (textLower.startsWith(queryLower)) return 90 + result.popularity;
  
  // Contains match bonus
  if (textLower.includes(queryLower)) return 80 + result.popularity;
  
  // Fuzzy match scoring
  const distance = levenshteinDistance(queryLower, textLower);
  const maxLength = Math.max(queryLower.length, textLower.length);
  const similarity = (maxLength - distance) / maxLength;
  
  // Keyword matching
  const keywordMatches = result.keywords.filter(keyword => 
    keyword.toLowerCase().includes(queryLower) || 
    queryLower.includes(keyword.toLowerCase())
  ).length;
  
  // Final score calculation
  const fuzzyScore = similarity * 70;
  const keywordScore = keywordMatches * 10;
  const popularityScore = result.popularity * 0.1;
  
  return Math.max(0, fuzzyScore + keywordScore + popularityScore);
};
*/

const ModernSearchEngine: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  // Advanced search database - aligned with available routes
  const searchDatabase: SearchResult[] = useMemo(() => [
    { id: '1', text: 'house cleaning', category: 'service', score: 0, keywords: ['home', 'residential', 'domestic', 'regular'], popularity: 95 },
    { id: '2', text: 'deep cleaning service', category: 'service', score: 0, keywords: ['thorough', 'detailed', 'intensive'], popularity: 90 },
    { id: '3', text: 'office cleaning', category: 'service', score: 0, keywords: ['commercial', 'workplace', 'business'], popularity: 85 },
    { id: '4', text: 'carpet cleaning', category: 'service', score: 0, keywords: ['rug', 'floor', 'upholstery'], popularity: 80 },
    { id: '5', text: 'window cleaning', category: 'service', score: 0, keywords: ['glass', 'exterior', 'interior'], popularity: 75 },
    { id: '6', text: 'move out cleaning', category: 'service', score: 0, keywords: ['relocation', 'moving', 'end of lease'], popularity: 85 },
    { id: '7', text: 'post construction cleanup', category: 'service', score: 0, keywords: ['renovation', 'building', 'debris'], popularity: 70 },
    { id: '8', text: 'sanitization service', category: 'service', score: 0, keywords: ['disinfection', 'sterilization', 'hygiene'], popularity: 88 },
    { id: '9', text: 'same day cleaning', category: 'trending', score: 0, keywords: ['urgent', 'immediate', 'emergency'], popularity: 82 },
    { id: '10', text: 'weekly cleaning service', category: 'service', score: 0, keywords: ['regular', 'recurring', 'maintenance'], popularity: 92 },
    { id: '11', text: 'pressure washing', category: 'service', score: 0, keywords: ['power wash', 'exterior', 'driveway'], popularity: 78 },
    { id: '12', text: 'upholstery cleaning', category: 'service', score: 0, keywords: ['furniture', 'fabric', 'sofa'], popularity: 72 },
    { id: '13', text: 'event cleaning', category: 'service', score: 0, keywords: ['party', 'celebration', 'gathering'], popularity: 68 },
    { id: '14', text: 'pool cleaning', category: 'service', score: 0, keywords: ['swimming', 'water', 'maintenance'], popularity: 65 },
    { id: '15', text: 'chimney cleaning', category: 'service', score: 0, keywords: ['fireplace', 'flue', 'heating'], popularity: 60 },
    { id: '16', text: 'corporate cleaning', category: 'service', score: 0, keywords: ['business', 'enterprise', 'large office'], popularity: 83 },
    { id: '17', text: 'industrial cleaning', category: 'service', score: 0, keywords: ['factory', 'warehouse', 'manufacturing'], popularity: 75 },
  ], []);

  // Real-time search with advanced algorithms
  const searchResults: SearchResult[] = useMemo(() => {
    if (!searchQuery.trim()) {
      // Return trending and recent searches when no query
      const trending = searchDatabase
        .filter(item => item.category === 'trending')
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, 3);
      
      const popular = searchDatabase
        .filter(item => item.category === 'service')
        .sort((a, b) => b.popularity - a.popularity)
        .slice(0, 5);
      
      return [...trending, ...popular];
    }

    // Calculate scores and sort by relevance
    const ranked = advancedRank(searchQuery, searchDatabase)
       .slice(0, 8)
       .map(r => {
         const { _score, ...rest } = r;
         return { ...rest, score: Math.round(_score * 100) } as SearchResult;
       });
    return ranked;
  }, [searchQuery, searchDatabase]);

  // Auto-suggestions based on partial input
  const autoSuggestions = useMemo(() => {
    if (searchQuery.length < 2) return [];
    
    return searchDatabase
      .filter(item => {
        const query = searchQuery.toLowerCase();
        const text = item.text.toLowerCase();
        return text.includes(query) && text !== query;
      })
      .sort((a, b) => b.popularity - a.popularity)
      .slice(0, 3)
      .map(item => item.text);
  }, [searchQuery, searchDatabase]);

  const handleSearch = (query: string = searchQuery) => {
    if (query.trim()) {
      // Add to recent searches with analytics
      const newRecentSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
      setRecentSearches(newRecentSearches);
      localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
      
      // Smart routing based on search query
      const queryLower = query.toLowerCase();
      let targetRoute = '/solutions'; // Default fallback
      
      // Residential services routing
      if (queryLower.includes('house') || queryLower.includes('home') || queryLower.includes('regular')) {
        targetRoute = '/residential/regular';
      } else if (queryLower.includes('deep cleaning') || queryLower.includes('thorough')) {
        targetRoute = '/residential/deep';
      } else if (queryLower.includes('carpet') || queryLower.includes('rug')) {
        targetRoute = '/residential/carpet';
      } else if (queryLower.includes('move out') || queryLower.includes('move-out') || queryLower.includes('moving')) {
        targetRoute = '/residential/moveout';
      } else if (queryLower.includes('window') || queryLower.includes('glass')) {
        targetRoute = '/residential/window';
      } else if (queryLower.includes('pressure wash') || queryLower.includes('power wash')) {
        targetRoute = '/residential/pressure';
      } else if (queryLower.includes('post construction') || queryLower.includes('construction') || queryLower.includes('renovation')) {
        targetRoute = '/residential/postconstruction';
      } else if (queryLower.includes('upholstery') || queryLower.includes('furniture')) {
        targetRoute = '/residential/upholstery';
      } else if (queryLower.includes('sanitization') || queryLower.includes('disinfect')) {
        targetRoute = '/residential/sanitization';
      } else if (queryLower.includes('pool')) {
        targetRoute = '/residential/pool';
      } else if (queryLower.includes('event') || queryLower.includes('party')) {
        targetRoute = '/residential/event';
      } else if (queryLower.includes('chimney')) {
        targetRoute = '/residential/chimney';
      }
      
      // Commercial services routing
      else if (queryLower.includes('office') || queryLower.includes('workplace')) {
        targetRoute = '/commercial/office';
      } else if (queryLower.includes('corporate') || queryLower.includes('business')) {
        targetRoute = '/commercial/corporate';
      } else if (queryLower.includes('industrial') || queryLower.includes('factory')) {
        targetRoute = '/commercial/industrial';
      } else if (queryLower.includes('commercial')) {
        targetRoute = '/commercial';
      }
      
      // Add location parameter if provided
      if (zipCode.trim()) {
        targetRoute += `?location=${encodeURIComponent(zipCode)}&q=${encodeURIComponent(query)}`;
      } else {
        targetRoute += `?q=${encodeURIComponent(query)}`;
      }
      
      navigate(targetRoute);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    
    // Add to recent searches
    const newRecentSearches = [suggestion, ...recentSearches.filter(s => s !== suggestion)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
    
    // Direct routing for known service suggestions
    const suggestionLower = suggestion.toLowerCase();
    let targetRoute = '/solutions'; // Default fallback
    
    // Map specific suggestions to routes
    if (suggestionLower === 'house cleaning' || suggestionLower === 'regular house cleaning') {
      targetRoute = '/residential/regular';
    } else if (suggestionLower === 'deep cleaning service' || suggestionLower === 'deep cleaning') {
      targetRoute = '/residential/deep';
    } else if (suggestionLower === 'office cleaning') {
      targetRoute = '/commercial/office';
    } else if (suggestionLower === 'carpet cleaning') {
      targetRoute = '/residential/carpet';
    } else if (suggestionLower === 'window cleaning') {
      targetRoute = '/residential/window';
    } else if (suggestionLower === 'move out cleaning' || suggestionLower === 'move-out cleaning') {
      targetRoute = '/residential/moveout';
    } else if (suggestionLower === 'post construction cleanup') {
      targetRoute = '/residential/postconstruction';
    } else if (suggestionLower === 'sanitization service') {
      targetRoute = '/residential/sanitization';
    } else if (suggestionLower === 'same day cleaning') {
      targetRoute = '/residential/regular';
    } else if (suggestionLower === 'weekly cleaning service') {
      targetRoute = '/residential/regular';
    } else {
      // Use the smart search logic for other queries
      handleSearch(suggestion);
      return;
    }
    
    // Add query parameter
    if (zipCode.trim()) {
      targetRoute += `?location=${encodeURIComponent(zipCode)}&q=${encodeURIComponent(suggestion)}`;
    } else {
      targetRoute += `?q=${encodeURIComponent(suggestion)}`;
    }
    
    navigate(targetRoute);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleInputFocus = () => {
    setShowSuggestions(true);
  };

  const handleInputBlur = () => {
    setTimeout(() => {
      setShowSuggestions(false);
    }, 200);
  };

  // Load recent searches on mount
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }
  }, []);

  return (
    <>
      <style>{`
        .advanced-glass-search {
          background: white;
          border: 1px solid rgba(209, 213, 219, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          transition: all 0.2s ease;
        }
        .advanced-glass-search:focus-within {
          background: white;
          border-color: rgba(156, 163, 175, 1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .zipcode-glass {
          background: rgba(249, 250, 251, 1);
          border: 1px solid rgba(209, 213, 219, 1);
        }
        .suggestions-glass {
          background: white;
          border: 1px solid rgba(209, 213, 219, 1);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        /* Google-style Clean Suggestions with Glass Effect */
        .google-suggestion {
          background: transparent;
          border: none;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          transition: all 0.2s ease;
          border-radius: 0;
          position: relative;
          overflow: hidden;
        }
        .google-suggestion:last-child {
          border-bottom: none;
        }
        .google-suggestion:hover {
          background: rgba(0, 0, 0, 0.05);
          border-bottom-color: rgba(0, 0, 0, 0.1);
        }
        .google-suggestion::after {
          content: '';
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          width: 16px;
          height: 16px;
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 4px;
          opacity: 0;
          transition: all 0.2s ease;
        }
        .google-suggestion:hover::after {
          opacity: 1;
          background: rgba(255, 255, 255, 0.1);
        }
        .google-suggestion::before {
          content: '⏎';
          position: absolute;
          right: 18px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 10px;
          color: rgba(255, 255, 255, 0.6);
          opacity: 0;
          transition: all 0.2s ease;
        }
        .google-suggestion:hover::before {
          opacity: 1;
        }
        .suggestion-item-enter {
          animation: slideInFade 0.3s ease-out forwards;
        }
        @keyframes slideInFade {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .typing-indicator {
          animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }
        .popular-pill {
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          overflow: hidden;
        }
        .popular-pill:hover {
          transform: translateY(-1px);
        }
        .popular-pill::after {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%);
          opacity: 0;
          transition: opacity 0.2s ease;
        }
        .popular-pill:hover::after {
          opacity: 1;
        }
        /* Enhanced hover effects for green accents */
        .popular-pill:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(16, 185, 129, 0.1);
        }
        .service-icon {
          transition: all 0.2s ease;
        }
        .auto-complete-pill {
          transition: all 0.2s ease;
        }
        .category-badge {
          background: rgba(249, 250, 251, 1);
          border: 1px solid rgba(209, 213, 219, 1);
          color: rgba(107, 114, 128, 1);
          font-weight: 500;
        }
        .trending-badge {
          background: rgba(249, 250, 251, 1);
          border: 1px solid rgba(209, 213, 219, 1);
          color: rgba(107, 114, 128, 1);
          font-weight: 500;
        }
        .icon-container {
          background: rgba(249, 250, 251, 1);
          border: 1px solid rgba(209, 213, 219, 1);
          transition: all 0.2s ease;
        }
        .suggestion-item:hover .icon-container {
          background: rgba(243, 244, 246, 1);
          border-color: rgba(156, 163, 175, 1);
        }
      `}</style>

      <div className="w-full max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.16, 1, 0.3, 1] }}
          className="relative"
        >
                    {/* Modern Search Bar - Enhanced Bento Style */}
          <div className="advanced-glass-search rounded-2xl p-3">
            <div className="flex items-stretch gap-4">
              {/* Main Search Input */}
              <div className="flex-1 flex items-center">
                <Search className="w-5 h-5 text-gray-400 mx-3 flex-shrink-0" />
                <div className="relative flex-1">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onFocus={handleInputFocus}
                    onBlur={handleInputBlur}
                    onKeyPress={handleKeyPress}
                    placeholder="What cleaning service do you need? (e.g., deep clean my kitchen)"
                    className="w-full bg-transparent text-gray-900 placeholder-gray-400 text-base focus:outline-none font-normal pr-8"
                  />
                  {searchQuery.length > 0 && searchQuery.length < 3 && (
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <div className="typing-indicator text-gray-400 text-sm flex items-center">
                        <span className="inline-block w-1 h-1 bg-current rounded-full animate-pulse mx-0.5"></span>
                        <span className="inline-block w-1 h-1 bg-current rounded-full animate-pulse mx-0.5" style={{animationDelay: '0.2s'}}></span>
                        <span className="inline-block w-1 h-1 bg-current rounded-full animate-pulse mx-0.5" style={{animationDelay: '0.4s'}}></span>
                      </div>
                    </div>
                  )}

                </div>
              </div>
              
              {/* Zipcode Input */}
              <div className="hidden sm:flex items-center">
                <div className="flex items-center zipcode-glass rounded-xl px-3 py-2.5">
                  <MapPin className="w-4 h-4 text-gray-400 mr-2 flex-shrink-0" />
                  <input
                    type="text"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
                    placeholder="ZIP"
                    className="bg-transparent text-gray-900 placeholder-gray-400 text-sm focus:outline-none w-14 font-medium"
                    maxLength={5}
                  />
                </div>
              </div>

              {/* Search Button */}
              <motion.button
                whileHover={{ scale: 1.01 }}
                whileTap={{ scale: 0.99 }}
                onClick={() => handleSearch()}
                className="bg-emerald-800 hover:bg-emerald-900 text-white px-6 py-3 rounded-xl font-medium transition-all flex items-center gap-2 shadow-sm hover:shadow-md hover:shadow-emerald-300"
              >
                Search
                <ArrowRight className="w-4 h-4" />
              </motion.button>
            </div>

            {/* Mobile Zipcode */}
            <div className="sm:hidden mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center zipcode-glass rounded-xl px-3 py-2.5">
                <MapPin className="w-4 h-4 text-gray-400 mr-3 flex-shrink-0" />
                <input
                  type="text"
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value.replace(/\D/g, '').slice(0, 5))}
                  placeholder="Enter your ZIP code"
                  className="bg-transparent text-gray-900 placeholder-gray-400 text-sm focus:outline-none w-full font-medium"
                  maxLength={5}
                />
              </div>
            </div>
          </div>

                    {/* Suggestions Dropdown */}
          <AnimatePresence>
            {showSuggestions && searchResults.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 8 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full left-0 right-0 mt-3 suggestions-glass rounded-2xl p-4 z-50"
              >
                {autoSuggestions.length > 0 && (
                  <div className="mb-4 px-4 pb-3 border-b border-gray-200">
                    <div className="flex items-center gap-2 mb-3">
                      <Zap className="w-4 h-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-900">Quick suggestions</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {autoSuggestions.map((suggestion, index) => (
                        <motion.button
                          key={suggestion}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ 
                            delay: index * 0.05,
                            duration: 0.2,
                            ease: "easeOut"
                          }}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className="auto-complete-pill px-3 py-1.5 text-gray-700 text-sm font-normal bg-gray-50 hover:bg-gray-100 rounded-xl transition-colors border border-gray-200 hover:border-gray-300"
                        >
                          {suggestion}
                        </motion.button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Search Results Header */}
                {searchResults.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="px-4 py-2 border-b border-gray-200 mb-2"
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-600 font-medium">
                        {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                      </span>
                      <span className="text-xs text-gray-500 flex items-center gap-1">
                        <span>Press</span>
                        <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs text-gray-700">Enter</kbd>
                        <span>to select</span>
                      </span>
                    </div>
                  </motion.div>
                )}

                {/* Google-style Search Results */}
                <div className="px-0">
                  {searchResults.slice(0, 8).map((result, index) => {
                    // Generate helpful descriptions
                    const getServiceDescription = (text: string) => {
                      const descriptions = {
                        'deep cleaning': 'Comprehensive cleaning service',
                        'house cleaning': 'Regular home maintenance',
                        'office cleaning': 'Professional workspace cleaning',
                        'move out cleaning': 'End of tenancy cleaning',
                        'sanitization': 'Health & safety focused service',
                        'pressure washing': 'Exterior surface cleaning',
                        'window cleaning': 'Glass & window maintenance',
                        'carpet cleaning': 'Deep fabric restoration'
                      };
                      
                      const key = Object.keys(descriptions).find(k => 
                        text.toLowerCase().includes(k)
                      );
                      return key ? descriptions[key as keyof typeof descriptions] : 'Professional cleaning service';
                    };

                    return (
                      <motion.button
                        key={result.id}
                        initial={{ opacity: 0, y: 12, scale: 0.98 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{ 
                          delay: index * 0.04, 
                          duration: 0.3,
                          ease: [0.25, 0.46, 0.45, 0.94]
                        }}
                        whileHover={{ scale: 1.005 }}
                        onClick={() => handleSuggestionClick(result.text)}
                        className="google-suggestion w-full text-left px-4 py-3 flex items-center gap-3 group border-none"
                      >
                        <motion.div 
                          className="flex-shrink-0 w-5 h-5 flex items-center justify-center"
                          whileHover={{ rotate: 15 }}
                          transition={{ duration: 0.2 }}
                        >
                          {result.category === 'trending' ? (
                            <TrendingUp className="w-4 h-4 text-blue-400" />
                          ) : result.category === 'recent' ? (
                            <svg className="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                            </svg>
                          ) : (
                            <Search className="w-4 h-4 text-gray-300" />
                          )}
                        </motion.div>
                        <div className="flex-1 min-w-0">
                          <span className="text-gray-900 font-normal text-base block truncate group-hover:text-blue-600 transition-colors">
                            {result.text}
                          </span>
                          <p className="text-gray-500 text-xs mt-0.5 truncate">
                            {getServiceDescription(result.text)}
                          </p>
                          <div className="flex items-center gap-3 mt-1">
                            <span className="text-xs text-gray-400">
                              Click to get quote
                            </span>
                          </div>
                        </div>
                      </motion.button>
                    );
                  })}
                </div>

                {/* No Results State */}
                {searchQuery.length > 2 && searchResults.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="px-4 py-8 text-center"
                  >
                    <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-gray-100 flex items-center justify-center">
                      <Search className="w-5 h-5 text-gray-400" />
                    </div>
                    <p className="text-gray-600 text-sm mb-2">No services found for "{searchQuery}"</p>
                    <p className="text-gray-500 text-xs">Try searching for "deep cleaning", "office cleaning", or "move out"</p>
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Popular Searches - Redesigned with UX principles */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-12"
        >
          <div className="text-center mb-6">
            <h3 className="text-base font-semibold text-gray-900 mb-2">Popular searches</h3>
            <p className="text-sm text-gray-500">Click any service to get an instant quote</p>
          </div>
          
          {/* Popular Services with Dark Green Accents */}
          <div className="flex flex-wrap gap-3 justify-center">
            {[
              { term: 'Regular house cleaning', path: '/residential/regular' },
              { term: 'Deep cleaning service', path: '/residential/deep' },
              { term: 'Office cleaning', path: '/commercial/office' },
              { term: 'Move-out cleaning', path: '/residential/moveout' },
              { term: 'Carpet cleaning', path: '/residential/carpet' },
              { term: 'Window cleaning', path: '/residential/window' },
            ].map(({ term, path }, index) => (
              <motion.button
                key={term}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 + index * 0.05 }}
                onClick={() => navigate(path)}
                className="group relative popular-pill flex items-center gap-3 px-4 py-3 rounded-xl text-gray-800 text-sm font-medium bg-white hover:bg-gray-50 border border-gray-200 hover:border-emerald-200 shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="relative">
                                              <div className="w-2.5 h-2.5 bg-emerald-800 rounded-full group-hover:bg-emerald-900 transition-colors duration-200" />
                            <div className="absolute inset-0 w-2.5 h-2.5 bg-emerald-600 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-200" />
                </div>
                <span className="group-hover:text-gray-900 relative">
                  {term}
                  <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-emerald-800 group-hover:w-full transition-all duration-200" />
                </span>
                <div className="absolute top-0 right-0 w-1 h-full bg-gradient-to-b from-emerald-800 to-emerald-900 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-r-xl" />
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default ModernSearchEngine; 