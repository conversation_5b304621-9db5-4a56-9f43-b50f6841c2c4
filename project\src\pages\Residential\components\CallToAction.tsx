import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Sparkles } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function CallToAction() {
  const navigate = useNavigate();

  const handleBookNow = () => {
    navigate('/residential/regular');
  };

  return (
    <section className="py-28 sm:py-36">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="relative text-center"
        >
          <div className="relative z-10 flex flex-col items-center">
            <motion.div 
              className="w-16 h-16 bg-emerald-100 rounded-2xl mb-8 border border-emerald-200 flex items-center justify-center shadow-md"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1, rotate: 360 }}
          viewport={{ once: true }}
              transition={{ type: 'spring', stiffness: 260, damping: 15, delay: 0.2 }}
            >
              <Sparkles 
                className="w-8 h-8" 
                color="#10b981"
                fill="none"
                stroke="#10b981"
                strokeWidth="2"
              />
            </motion.div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-5">Ready for a Spotless Home?</h2>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto mb-10">
              Join thousands of satisfied customers who trust us for a consistently clean and peaceful home.
            </p>

            <motion.button
              onClick={handleBookNow}
              whileHover={{ scale: 1.05, boxShadow: '0 10px 30px rgba(16, 185, 129, 0.3)' }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              className="group px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 flex items-center gap-3 bg-emerald-600 text-white shadow-lg hover:bg-emerald-700"
          >
              Book Your First Cleaning
              <ArrowRight 
                className="w-5 h-5 transition-transform group-hover:translate-x-1" 
                color="#ffffff"
                fill="none"
                stroke="#ffffff"
                strokeWidth="2"
              />
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
