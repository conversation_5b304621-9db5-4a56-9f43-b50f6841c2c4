import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../../components/ui/Button';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';

const BrandAlignedIndustrialForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSubmitting, ] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, ] = useState({
    serviceType: 'industrial',
    facilitySize: '',
    machineryCleaning: false,
  });

  const calculatePrice = () => 750;

  const handleSubmit = async () => {
    if (!user) {
      navigate('/auth/login', { state: { from: '/commercial/industrial' } });
      return;
    }
    setShowPaymentModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <h1 className="text-2xl font-bold text-center p-4">Industrial Cleaning Booking</h1>
      <div className="p-8">
        <p>This is the form for Industrial Cleaning.</p>
        <Button onClick={handleSubmit} disabled={isSubmitting} className="mt-4">
          {isSubmitting ? 'Submitting...' : 'Book Now'}
        </Button>
      </div>

      {showPaymentModal && (
        <PaymentOptionsModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          amount={calculatePrice()}
          description="Industrial Cleaning Service"
          formData={formData}
          user={user}
        />
      )}
    </div>
  );
};

export default BrandAlignedIndustrialForm; 
