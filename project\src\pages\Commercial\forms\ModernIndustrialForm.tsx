import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Factory, Truck, Shield, CheckCircle, AlertCircle, HardHat, ArrowRight
} from 'lucide-react';
import { Button } from '../../../components/ui/Button';
import { AnimatedBackground } from '../../../components/layout/AnimatedBackground';


interface IndustrialFormData {
  servicePackage: string;
  facilityType: string;
  industryType: string;
  facilitySize: number;
  hazardousMaterials: string[];
  safetyRequirements: string[];
  accessRestrictions: string;
  operatingHours: string;
  facilityAddress: string;
  serviceFrequency: string;
  preferredTime: string;
  priorityAreas: string[];
  additionalServices: string[];
  specialInstructions: string;
  emergencyContact: string;
  complianceStandards: string[];
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  facilityAddress?: string;
  facilityType?: string;
  industryType?: string;
  facilitySize?: string;
  serviceFrequency?: string;
  preferredTime?: string;
  servicePackage?: string;
}

const steps = [
  { id: 1, name: 'Service Type' },
  { id: 2, name: 'Facility Details' },
  { id: 3, name: 'Safety & Compliance' },
  { id: 4, name: 'Service Schedule' },
  { id: 5, name: 'Additional Services' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'standard', name: 'Standard Industrial Package', description: 'Basic cleaning for manufacturing facilities', icon: <Factory /> },
  { id: 'heavy-duty', name: 'Heavy-Duty Industrial', description: 'Deep cleaning for heavy manufacturing', icon: <Truck /> },
  { id: 'hazmat', name: 'Hazmat Specialized', description: 'Specialized cleaning for hazardous environments', icon: <Shield /> },
  { id: 'warehouse', name: 'Warehouse Package', description: 'Large-scale warehouse and distribution cleaning', icon: <HardHat /> },
];

const facilityTypes = [
  { id: 'manufacturing', name: 'Manufacturing Plant' },
  { id: 'warehouse', name: 'Warehouse/Distribution' },
  { id: 'chemical', name: 'Chemical Processing' },
  { id: 'food-processing', name: 'Food Processing' },
  { id: 'automotive', name: 'Automotive Assembly' },
  { id: 'pharmaceutical', name: 'Pharmaceutical' },
];

const industryTypes = [
  { id: 'automotive', name: 'Automotive' },
  { id: 'food-beverage', name: 'Food & Beverage' },
  { id: 'chemical', name: 'Chemical' },
  { id: 'pharmaceutical', name: 'Pharmaceutical' },
  { id: 'manufacturing', name: 'General Manufacturing' },
  { id: 'logistics', name: 'Logistics & Distribution' },
];

// Enhanced Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Enhanced Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

const ModernIndustrialForm: React.FC = () => {
  const navigate = useNavigate();

  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<IndustrialFormData>>({
    servicePackage: 'standard',
    hazardousMaterials: [],
    safetyRequirements: [],
    priorityAreas: [],
    additionalServices: [],
    complianceStandards: [],
    facilitySize: 10000,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('industrialFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('industrialFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'facilityAddress':
        if (!value) return 'Facility address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'facilityType':
        if (!value) return 'Please select a facility type';
        break;
      case 'industryType':
        if (!value) return 'Please select an industry type';
        break;
      case 'facilitySize':
        if (!value || parseInt(value) < 1000) return 'Facility size must be at least 1000 sq ft';
        break;
      case 'serviceFrequency':
        if (!value) return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value) return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 1) {
        if (!formData.servicePackage) {
          setFormErrors({ servicePackage: 'Please select a service package' });
          setIsLoading(false);
          return;
        }
      } else if (currentStep === 2) {
        errors.facilityType = validateField('facilityType', formData.facilityType || '');
        errors.industryType = validateField('industryType', formData.industryType || '');
        errors.facilitySize = validateField('facilitySize', formData.facilitySize?.toString() || '');
        errors.facilityAddress = validateField('facilityAddress', formData.facilityAddress || '');
      } else if (currentStep === 4) {
        errors.serviceFrequency = validateField('serviceFrequency', formData.serviceFrequency || '');
        errors.preferredTime = validateField('preferredTime', formData.preferredTime || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
        // Scroll to first error
        setTimeout(() => {
          const firstErrorElement = document.querySelector('.text-red-500');
          if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage && !formErrors.servicePackage;
      case 2:
        return formData.facilityType && 
               formData.industryType && 
               formData.facilitySize && 
               formData.facilityAddress &&
               !formErrors.facilityType &&
               !formErrors.industryType &&
               !formErrors.facilitySize &&
               !formErrors.facilityAddress;
      case 4:
        return formData.serviceFrequency && 
               formData.preferredTime &&
               !formErrors.serviceFrequency &&
               !formErrors.preferredTime;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data for estimate scheduling
      const estimateData = {
        ...formData,
        serviceType: 'industrial-cleaning',
        requestType: 'estimate',
        submittedAt: new Date().toISOString(),
        contactInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle
        },
        facilityInfo: {
          address: formData.facilityAddress,
          facilityType: formData.facilityType,
          industryType: formData.industryType,
          facilitySize: formData.facilitySize
        }
      };
      
      console.log('Scheduling industrial estimate with data:', estimateData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Clear form data from localStorage after successful submission
      localStorage.removeItem('industrialFormData');
      
      // Navigate to thank you page
      navigate('/commercial/thank-you');
      
    } catch (error) {
      console.error('Error scheduling industrial estimate:', error);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Industrial Cleaning Services
            </h1>
            <p className="text-gray-600">Specialized cleaning solutions for industrial facilities and manufacturing plants.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-gray-900 shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-gray-900" style={{ color: 'white' }} /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Service Package Selection */}
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-900 mb-3">Choose Your Industrial Package</h2>
                    <p className="text-gray-600 text-lg">Select the industrial cleaning package that meets your facility needs</p>
                    {formErrors.servicePackage && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex items-center justify-center mt-4 text-red-500"
                      >
                        <AlertCircle className="w-5 h-5 mr-2" />
                        <span>{formErrors.servicePackage}</span>
                      </motion.div>
                    )}
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 mb-8">
                    {servicePackages.map((pkg) => (
                      <motion.div
                        key={pkg.id}
                        className={`group relative p-6 rounded-xl cursor-pointer transition-all duration-200 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                            : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                        } border-2`}
                        onClick={() => {
                          setFormData({ ...formData, servicePackage: pkg.id });
                          // Clear any service package error
                          if (formErrors.servicePackage) {
                            setFormErrors(prev => ({ ...prev, servicePackage: undefined }));
                          }
                        }}
                        whileHover={{ scale: 1.02, y: -2 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {formData.servicePackage === pkg.id && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-4 right-4"
                          >
                            <CheckCircle className="w-6 h-6 text-emerald-600" />
                          </motion.div>
                        )}
                        
                        <div className="flex items-center mb-4">
                          <div className={`p-2 rounded-xl mr-4 transition-colors duration-200 ${
                            formData.servicePackage === pkg.id 
                              ? 'bg-emerald-100 text-emerald-800' 
                              : 'bg-emerald-50 text-emerald-700'
                          }`}>
                            {React.cloneElement(pkg.icon as React.ReactElement, { className: "w-6 h-6" })}
                          </div>
                          <h3 className="text-xl font-bold text-gray-900">{pkg.name}</h3>
                        </div>
                        <p className="text-gray-600">{pkg.description}</p>
                      </motion.div>
                    ))}
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={() => navigate('/')}
                      className="px-6 py-3 rounded-xl border-gray-300 text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Facility Details */}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Facility Details</h2>
                  <p className="text-gray-600 mb-6">Tell us about your industrial facility</p>

                  <div className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-gray-900 font-medium mb-2">
                          Facility Type <span className="text-red-500">*</span>
                        </label>
                        <select 
                          className={`w-full bg-white p-3 rounded-xl border text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                            formErrors.facilityType 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-gray-200 focus:border-emerald-400'
                          }`}
                          value={formData.facilityType || ''}
                          onChange={(e) => {
                            setFormData({...formData, facilityType: e.target.value});
                            const error = validateField('facilityType', e.target.value);
                            setFormErrors(prev => ({ ...prev, facilityType: error }));
                          }}
                        >
                          <option value="">Select facility type</option>
                          {facilityTypes.map(type => (
                            <option key={type.id} value={type.id} style={{ color: 'black' }}>{type.name}</option>
                          ))}
                        </select>
                        {formErrors.facilityType && (
                          <div className="flex items-center mt-1 text-red-500 text-sm">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.facilityType}
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-gray-900 font-medium mb-2">
                          Industry Type <span className="text-red-500">*</span>
                        </label>
                        <select 
                          className={`w-full bg-white p-3 rounded-xl border text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                            formErrors.industryType 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-gray-200 focus:border-emerald-400'
                          }`}
                          value={formData.industryType || ''}
                          onChange={(e) => {
                            setFormData({...formData, industryType: e.target.value});
                            const error = validateField('industryType', e.target.value);
                            setFormErrors(prev => ({ ...prev, industryType: error }));
                          }}
                        >
                          <option value="">Select industry</option>
                          {industryTypes.map(industry => (
                            <option key={industry.id} value={industry.id} style={{ color: 'black' }}>{industry.name}</option>
                          ))}
                        </select>
                        {formErrors.industryType && (
                          <div className="flex items-center mt-1 text-red-500 text-sm">
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {formErrors.industryType}
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Facility Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="e.g., 123 Industrial Blvd, Manufacturing District"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.facilityAddress 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.facilityAddress || ''}
                        onChange={(e) => {
                          setFormData({...formData, facilityAddress: e.target.value});
                          const error = validateField('facilityAddress', e.target.value);
                          setFormErrors(prev => ({ ...prev, facilityAddress: error }));
                        }}
                      />
                      {formErrors.facilityAddress && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.facilityAddress}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Facility Size (sq ft) <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        min="1000"
                        placeholder="e.g., 50000"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.facilitySize 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.facilitySize || ''}
                        onChange={(e) => {
                          setFormData({...formData, facilitySize: parseInt(e.target.value) || 0});
                          const error = validateField('facilitySize', e.target.value);
                          setFormErrors(prev => ({ ...prev, facilitySize: error }));
                        }}
                      />
                      {formErrors.facilitySize && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.facilitySize}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Validation Summary for Step 2 */}
                  {(formErrors.facilityType || formErrors.industryType || formErrors.facilitySize || formErrors.facilityAddress) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-50 border border-red-200 rounded-xl p-4 mt-6"
                    >
                      <h4 className="text-red-700 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-600 text-sm space-y-1">
                        {formErrors.facilityType && <li>• {formErrors.facilityType}</li>}
                        {formErrors.industryType && <li>• {formErrors.industryType}</li>}
                        {formErrors.facilitySize && <li>• {formErrors.facilitySize}</li>}
                        {formErrors.facilityAddress && <li>• {formErrors.facilityAddress}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Safety & Compliance */}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Safety & Compliance</h2>
                  <p className="text-gray-600 mb-6">Industrial safety requirements and compliance standards</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-gray-900 font-medium mb-4">
                        Hazardous Materials Present (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Chemicals', 'Flammable Materials', 'Heavy Metals', 'Radioactive Materials', 'Biological Hazards', 'None'].map((material) => (
                          <motion.button
                            key={material}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.hazardousMaterials?.includes(material)
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900 shadow-lg shadow-emerald-400/20'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40 hover:bg-white'
                            }`}
                            onClick={() => {
                              const current = formData.hazardousMaterials || [];
                              if (current.includes(material)) {
                                setFormData({...formData, hazardousMaterials: current.filter(h => h !== material)});
                              } else {
                                setFormData({...formData, hazardousMaterials: [...current, material]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.hazardousMaterials?.includes(material) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{material}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-4">
                        Required Safety Certifications (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['OSHA 10', 'OSHA 30', 'HAZWOPER', 'Confined Space', 'Lockout/Tagout', 'None Required'].map((cert) => (
                          <motion.button
                            key={cert}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.safetyRequirements?.includes(cert)
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900 shadow-lg shadow-emerald-400/20'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40 hover:bg-white'
                            }`}
                            onClick={() => {
                              const current = formData.safetyRequirements || [];
                              if (current.includes(cert)) {
                                setFormData({...formData, safetyRequirements: current.filter(s => s !== cert)});
                              } else {
                                setFormData({...formData, safetyRequirements: [...current, cert]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.safetyRequirements?.includes(cert) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{cert}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Access Restrictions or Special Requirements
                      </label>
                      <textarea
                        placeholder="e.g., Security clearance required, restricted hours, escort needed..."
                        className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-emerald-400 transition-colors duration-300 h-24"
                        value={formData.accessRestrictions || ''}
                        onChange={(e) => setFormData({...formData, accessRestrictions: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Service Schedule */}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Service Schedule</h2>
                  <p className="text-gray-600 mb-6">When do you need industrial cleaning services?</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Service Frequency <span className="text-red-500">*</span>
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['One-time', 'Weekly', 'Bi-weekly', 'Monthly', 'Quarterly', 'As Needed'].map((freq) => (
                          <button
                            key={freq}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                              formData.serviceFrequency === freq
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40'
                            }`}
                            onClick={() => {
                              setFormData({...formData, serviceFrequency: freq});
                              const error = validateField('serviceFrequency', freq);
                              setFormErrors(prev => ({ ...prev, serviceFrequency: error }));
                            }}
                          >
                            {freq}
                          </button>
                        ))}
                      </div>
                      {formErrors.serviceFrequency && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.serviceFrequency}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Preferred Time <span className="text-red-500">*</span>
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Night Shift (11PM-7AM)', 'Day Shift (7AM-3PM)', 'Evening Shift (3PM-11PM)', 'Weekend Only', 'Shutdown Periods', 'Flexible'].map((time) => (
                          <button
                            key={time}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 ${
                              formData.preferredTime === time
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40'
                            }`}
                            onClick={() => {
                              setFormData({...formData, preferredTime: time});
                              const error = validateField('preferredTime', time);
                              setFormErrors(prev => ({ ...prev, preferredTime: error }));
                            }}
                          >
                            {time}
                          </button>
                        ))}
                      </div>
                      {formErrors.preferredTime && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.preferredTime}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Operating Hours
                      </label>
                      <input
                        type="text"
                        placeholder="e.g., 24/7, Monday-Friday 6AM-6PM, Weekends Only"
                        className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.operatingHours || ''}
                        onChange={(e) => setFormData({...formData, operatingHours: e.target.value})}
                      />
                    </div>
                  </div>

                  {/* Validation Summary for Step 4 */}
                  {(formErrors.serviceFrequency || formErrors.preferredTime) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-xl p-4 mt-6"
                    >
                      <h4 className="text-red-500 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-300 text-sm space-y-1">
                        {formErrors.serviceFrequency && <li>• {formErrors.serviceFrequency}</li>}
                        {formErrors.preferredTime && <li>• {formErrors.preferredTime}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 5: Additional Services */}
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Additional Services</h2>
                  <p className="text-gray-600 mb-6">Optional services to enhance your industrial cleaning</p>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-gray-900 font-medium mb-4">
                        Priority Cleaning Areas (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Production Floor', 'Administrative Offices', 'Restrooms', 'Break Rooms', 'Loading Docks', 'Machinery Areas', 'Storage Areas', 'Control Rooms'].map((area) => (
                          <motion.button
                            key={area}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.priorityAreas?.includes(area)
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900 shadow-lg shadow-emerald-400/20'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40 hover:bg-white'
                            }`}
                            onClick={() => {
                              const current = formData.priorityAreas || [];
                              if (current.includes(area)) {
                                setFormData({...formData, priorityAreas: current.filter(p => p !== area)});
                              } else {
                                setFormData({...formData, priorityAreas: [...current, area]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.priorityAreas?.includes(area) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{area}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-4">
                        Additional Services (Select all that apply)
                      </label>
                      <div className="grid md:grid-cols-2 gap-4">
                        {['Deep Degreasing', 'Equipment Cleaning', 'Pressure Washing', 'Window Cleaning', 'Waste Removal', 'Disinfection Services', 'Carpet Cleaning', 'Floor Stripping/Waxing'].map((service) => (
                          <motion.button
                            key={service}
                            type="button"
                            className={`p-4 rounded-xl border-2 transition-all duration-300 text-left relative ${
                              formData.additionalServices?.includes(service)
                                ? 'border-emerald-400 bg-emerald-500/20 text-gray-900 shadow-lg shadow-emerald-400/20'
                                : 'border-gray-200 bg-white/5 text-gray-600 hover:border-white/40 hover:bg-white'
                            }`}
                            onClick={() => {
                              const current = formData.additionalServices || [];
                              if (current.includes(service)) {
                                setFormData({...formData, additionalServices: current.filter(s => s !== service)});
                              } else {
                                setFormData({...formData, additionalServices: [...current, service]});
                              }
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            {formData.additionalServices?.includes(service) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="absolute top-3 right-3 w-5 h-5 bg-emerald-400 rounded-full flex items-center justify-center"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                            <span className="font-medium">{service}</span>
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Special Instructions or Requirements
                      </label>
                      <textarea
                        placeholder="Any specific cleaning protocols, equipment to avoid, safety procedures..."
                        className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-emerald-400 transition-colors duration-300 h-32"
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Emergency Contact
                      </label>
                      <input
                        type="text"
                        placeholder="Name and phone number for emergencies"
                        className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.emergencyContact || ''}
                        onChange={(e) => setFormData({...formData, emergencyContact: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleNext}
                      disabled={isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      <>
                        Continue <ArrowRight className="ml-2 w-5 h-5" />
                      </>
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 6: Contact Information */}
              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Contact Information</h2>
                  <p className="text-gray-600 mb-6">Let's get your contact details to finalize your industrial estimate.</p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        First Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="John"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.firstName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.firstName || ''}
                        onChange={(e) => handleNameChange('firstName', e.target.value)}
                      />
                      {formErrors.firstName && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.firstName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Last Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="Smith"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.lastName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.lastName || ''}
                        onChange={(e) => handleNameChange('lastName', e.target.value)}
                      />
                      {formErrors.lastName && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.lastName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        placeholder="<EMAIL>"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.email 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.email || ''}
                        onChange={(e) => handleEmailChange(e.target.value)}
                      />
                      {formErrors.email && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.email}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Phone Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        placeholder="(*************"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.phone 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.phone || ''}
                        onChange={(e) => handlePhoneChange(e.target.value)}
                      />
                      {formErrors.phone && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.phone}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Company Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="ABC Manufacturing"
                        className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          formErrors.companyName 
                            ? 'border-red-400 focus:border-red-400' 
                            : 'border-gray-200 focus:border-emerald-400'
                        }`}
                        value={formData.companyName || ''}
                        onChange={(e) => handleCompanyNameChange(e.target.value)}
                      />
                      {formErrors.companyName && (
                        <div className="flex items-center mt-1 text-red-500 text-sm">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.companyName}
                        </div>
                      )}
                    </div>

                    <div>
                      <label className="block text-gray-900 font-medium mb-2">
                        Job Title
                      </label>
                      <input
                        type="text"
                        placeholder="Facilities Manager"
                        className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:border-emerald-400 transition-colors duration-300"
                        value={formData.jobTitle || ''}
                        onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                      />
                    </div>
                  </div>

                  {/* Validation Summary for Step 6 */}
                  {(formErrors.firstName || formErrors.lastName || formErrors.email || formErrors.phone || formErrors.companyName) && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-red-500/10 border border-red-400/20 rounded-xl p-4 mt-6"
                    >
                      <h4 className="text-red-500 font-medium mb-2">Please complete the following:</h4>
                      <ul className="text-red-300 text-sm space-y-1">
                        {formErrors.firstName && <li>• {formErrors.firstName}</li>}
                        {formErrors.lastName && <li>• {formErrors.lastName}</li>}
                        {formErrors.email && <li>• {formErrors.email}</li>}
                        {formErrors.phone && <li>• {formErrors.phone}</li>}
                        {formErrors.companyName && <li>• {formErrors.companyName}</li>}
                      </ul>
                    </motion.div>
                  )}

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        'Request Industrial Estimate'
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>


    </AnimatedBackground>
  );
};

export default ModernIndustrialForm; 

