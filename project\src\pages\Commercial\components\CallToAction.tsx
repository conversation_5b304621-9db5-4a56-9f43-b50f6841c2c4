import React from 'react';
import { motion } from 'framer-motion';
import { ArrowRight, Building } from 'lucide-react';

export function CallToAction() {
  const handleGetQuote = () => {
    const servicesSection = document.getElementById('services-section');
    if (servicesSection) {
      servicesSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <section className="py-28 sm:py-36">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.5 }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
          className="relative text-center"
        >
          <div className="relative z-10 flex flex-col items-center">
            <motion.div 
              className="w-16 h-16 bg-emerald-100 rounded-2xl mb-8 border border-emerald-200 flex items-center justify-center shadow-lg"
              initial={{ scale: 0 }}
              whileInView={{ scale: 1, rotate: 360 }}
          viewport={{ once: true }}
              transition={{ type: 'spring', stiffness: 260, damping: 15, delay: 0.2 }}
            >
              <Building className="w-8 h-8 text-emerald-800" />
            </motion.div>
            
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-5">Elevate Your Business Environment</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-10">
              Partner with us to create a clean, safe, and professional space that reflects the quality of your brand.
            </p>

            <motion.button
              onClick={handleGetQuote}
              whileHover={{ scale: 1.05, boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)' }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
              className="group px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 flex items-center gap-3 bg-emerald-600 text-white shadow-lg hover:bg-emerald-700"
          >
              View Our Services
              <motion.div
                animate={{ y: [0, -2, 0] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1 rotate-[-90deg]" />
              </motion.div>
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
} 
