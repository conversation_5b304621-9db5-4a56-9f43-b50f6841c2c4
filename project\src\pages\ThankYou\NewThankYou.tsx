import React from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useBookingData } from '../../hooks/useBookingData';
import { motion } from 'framer-motion';
import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { Button } from '../../components/ui/Button';
import { CheckCircle, AlertCircle, Home, ArrowRight } from 'lucide-react';

const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
      className="w-16 h-16 border-4 border-brand-200 border-t-brand-600 rounded-full"
    />
  </div>
);

const ErrorDisplay = ({ message }: { message: string }) => {
  const navigate = useNavigate();
  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center">
      <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
      <h1 className="text-2xl font-bold text-gray-800 mb-2">Something Went Wrong</h1>
      <p className="text-gray-600 mb-6">{message}</p>
      <Button onClick={() => navigate('/')}>
        <Home className="mr-2 w-5 h-5" />
        Return to Home
      </Button>
    </div>
  );
};

const BookingConfirmation = ({ bookingData }: { bookingData: any }) => {
  return (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="max-w-2xl mx-auto bg-white rounded-2xl shadow-lg p-8"
  >
    <div className="text-center mb-8">
      <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
      <h1 className="text-4xl font-bold text-gray-900">Booking Confirmed!</h1>
      <p className="text-lg text-gray-600 mt-2">
        Thank you, {bookingData.full_name}. Your {bookingData.service_type} is scheduled.
      </p>
    </div>
    <div className="space-y-4">
      <div className="p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800">Service Date</h3>
        <p className="text-gray-600">{new Date(bookingData.booking_date).toLocaleDateString()}</p>
      </div>
      <div className="p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800">Location</h3>
        <p className="text-gray-600">{bookingData.property_address}</p>
      </div>
      <div className="p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800">Confirmation Email</h3>
        <p className="text-gray-600">A confirmation has been sent to {bookingData.email}.</p>
      </div>
    </div>
            <div className="text-center mt-8">
      <Button size="lg" onClick={() => window.location.href = '/'}>
        <Home className="mr-2 w-5 h-5" />
        Return to Home
        <ArrowRight className="ml-2 w-5 h-5" />
      </Button>
    </div>
  </motion.div>
  )
};

export function NewThankYou() {
  const [searchParams] = useSearchParams();
  const bookingId = searchParams.get('bookingId');
  const { bookingData, isLoading, error } = useBookingData(bookingId);

    const navigate = useNavigate();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorDisplay message={error} />;
  }

  if (!bookingData) {
    // This handles the case where there's no bookingId or data is not found
    // You might want to redirect or show a specific message
    return <ErrorDisplay message="Booking not found." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-20 px-4">
        <BookingConfirmation bookingData={bookingData} />
      </main>
      <Footer />
    </div>
  );
}