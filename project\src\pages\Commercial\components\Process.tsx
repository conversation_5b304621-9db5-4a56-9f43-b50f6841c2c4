import React from 'react';
import { motion } from 'framer-motion';
import { FileText, Building, CheckCircle } from 'lucide-react';

const steps = [
  { icon: FileText, title: 'Request a Quote', description: 'Contact us with your requirements for a detailed, no-obligation quote.' },
  { icon: Building, title: 'On-Site Walkthrough', description: 'We conduct a walkthrough to create a customized cleaning plan.' },
  { icon: CheckCircle, title: 'Service & Support', description: 'Our team delivers exceptional service with ongoing support and quality checks.' }
];

export function Process() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Our Streamlined Commercial Process</h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            A seamless process from consultation to ongoing service delivery.
          </p>
        </motion.div>

        <div className="relative">
          {/* Connecting Line */}
          <div className="absolute left-1/2 top-6 bottom-6 w-0.5 bg-emerald-200 hidden md:block" />

          <motion.div 
            className="space-y-16"
            variants={{
              hidden: { opacity: 0 },
              visible: {
                opacity: 1,
                transition: {
                  staggerChildren: 0.3,
                  delayChildren: 0.2
                }
              }
            }}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
          {steps.map((step, index) => {
            const Icon = step.icon;
              const isEven = index % 2 === 0;

            return (
              <motion.div
                  key={step.title}
                  variants={{
                    hidden: { 
                      opacity: 0, 
                      x: isEven ? -100 : 100,
                      scale: 0.8
                    },
                    visible: { 
                      opacity: 1, 
                      x: 0,
                      scale: 1,
                      transition: {
                        type: "spring",
                        stiffness: 80,
                        damping: 15,
                        duration: 0.8
                      }
                    }
                  }}
                  whileHover={{ 
                    scale: 1.03,
                    transition: { 
                      type: "spring", 
                      stiffness: 300,
                      damping: 20 
                    }
                  }}
                  className={`relative flex flex-col md:flex-row items-center group ${isEven ? '' : 'md:flex-row-reverse'}`}
                >
                  {/* Step number indicator */}
                  <motion.div
                    className="absolute -top-4 left-1/2 transform -translate-x-1/2 md:hidden"
                    initial={{ scale: 0, rotate: -180 }}
                    whileInView={{ 
                      scale: 1, 
                      rotate: 0,
                      transition: { 
                        delay: index * 0.3 + 0.5,
                        type: "spring",
                        stiffness: 200
                      }
                    }}
                    viewport={{ once: true }}
                  >
                    <div className="w-8 h-8 bg-emerald-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg">
                      {index + 1}
                    </div>
                  </motion.div>

                  <motion.div 
                    className={`md:w-5/12 text-center ${isEven ? 'md:text-right md:pr-12' : 'md:text-left md:pl-12'} p-8 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 relative overflow-hidden cursor-pointer`}
                    style={{
                      background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
                    }}
                    whileHover={{
                      y: -5,
                      rotateY: isEven ? -2 : 2,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 20
                      }
                    }}
                  >
                    {/* Animated background overlay */}
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-br from-emerald-50/60 to-emerald-100/40 opacity-0 group-hover:opacity-100 transition-opacity duration-400 rounded-2xl"
                      initial={false}
                    />

                    {/* Sparkle effects */}
                    <div className="absolute inset-0 overflow-hidden rounded-2xl">
                      {[...Array(4)].map((_, i) => (
                        <motion.div
                          key={i}
                          className="absolute w-1 h-1 bg-emerald-300/60 rounded-full"
                          style={{
                            left: `${15 + i * 20}%`,
                            top: `${20 + i * 15}%`,
                          }}
                          animate={{
                            scale: [0, 1, 0],
                            opacity: [0, 1, 0],
                            rotate: [0, 180, 360]
                          }}
                          transition={{
                            duration: 2 + i * 0.3,
                            repeat: Infinity,
                            ease: "easeInOut",
                            delay: i * 0.4
                          }}
                        />
                      ))}
                    </div>

                    <div className="relative z-10">
                      <motion.h3 
                        className="text-2xl font-semibold text-gray-900 mb-3 group-hover:text-emerald-900 transition-colors duration-300"
                        whileHover={{ scale: 1.05 }}
                      >
                        {step.title}
                      </motion.h3>
                      <motion.p 
                        className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300"
                        whileHover={{ scale: 1.02 }}
                      >
                        {step.description}
                      </motion.p>
                    </div>
                  </motion.div>
                  
                  <motion.div 
                    className="flex-shrink-0 w-24 h-24 rounded-full bg-emerald-50 border-2 border-emerald-200 flex items-center justify-center my-4 md:my-0 z-10 shadow-lg relative overflow-hidden"
                    whileHover={{ 
                      scale: 1.1,
                      rotate: 5,
                      transition: {
                        type: "spring",
                        stiffness: 300,
                        damping: 15
                      }
                    }}
                    style={{
                      background: 'radial-gradient(circle at 30% 30%, #d1fae5, #a7f3d0)'
                    }}
                  >
                    {/* Pulsing ring effect */}
                    <motion.div
                      className="absolute inset-0 rounded-full border-2 border-emerald-300"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.6, 0.2, 0.6]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                    />

                    <motion.div 
                      className="w-16 h-16 rounded-full bg-emerald-100 border border-emerald-300 flex items-center justify-center shadow-sm relative z-10"
                      whileHover={{ 
                        rotate: 360,
                        transition: { duration: 0.6 }
                      }}
                      style={{
                        background: 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)'
                      }}
                    >
                      <motion.div
                        animate={{
                          rotate: [0, 5, -5, 0],
                          scale: [1, 1.05, 1]
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        <Icon className="w-8 h-8 text-emerald-800 group-hover:text-emerald-900 transition-colors duration-300" />
                      </motion.div>
                    </motion.div>

                    {/* Step number for desktop */}
                    <motion.div
                      className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-600 text-white rounded-full hidden md:flex items-center justify-center text-xs font-bold shadow-md"
                      initial={{ scale: 0, rotate: -180 }}
                      whileInView={{ 
                        scale: 1, 
                        rotate: 0,
                        transition: { 
                          delay: index * 0.3 + 0.7,
                          type: "spring",
                          stiffness: 200
                        }
                      }}
                      viewport={{ once: true }}
                    >
                      {index + 1}
                    </motion.div>
                  </motion.div>

                  <div className="md:w-5/12" />
              </motion.div>
            );
          })}
          </motion.div>
        </div>
      </div>
    </section>
  );
} 
