import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Castle, Warehouse, Heart, Gift, Zap, Users,
  Mail, Phone, MapPin
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  propertySize: string;
  bedrooms: string;
  bathrooms: string;
  frequency: string;
  cleaningType: string;
  specialRequests: string[];
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernRegularCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    propertySize: '',
    bedrooms: '',
    bathrooms: '',
    frequency: '',
    cleaningType: '',
    specialRequests: [],
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('residentialRegularFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('residentialRegularFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Service Options' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units',
      multiplier: 1.0
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes',
      multiplier: 1.2
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Castle, 
      description: 'Multi-level attached',
      multiplier: 1.15
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: Warehouse, 
      description: '3+ stories, 4+ bedrooms',
      multiplier: 1.5
    }
  ];

  const propertySizes = [
    { id: 'small', name: 'Small (< 1,200 sq ft)', basePrice: 85, multiplier: 0.8 },
    { id: 'medium', name: 'Medium (1,200 - 2,000 sq ft)', basePrice: 120, multiplier: 1.0 },
    { id: 'large', name: 'Large (2,000 - 3,000 sq ft)', basePrice: 165, multiplier: 1.3 },
    { id: 'xl', name: 'Extra Large (3,000+ sq ft)', basePrice: 220, multiplier: 1.7 }
  ];

  const frequencies = [
    { id: 'weekly', name: 'Weekly', discount: 0.15, description: 'Every week (15% savings)' },
    { id: 'biweekly', name: 'Bi-weekly', discount: 0.10, description: 'Every 2 weeks (10% savings)' },
    { id: 'monthly', name: 'Monthly', discount: 0.05, description: 'Once a month (5% savings)' },
    { id: 'one-time', name: 'One-time', discount: 0, description: 'Single cleaning' }
  ];

  const cleaningTypes = [
    { 
      id: 'standard', 
      name: 'Standard Clean', 
      price: 0, 
      description: 'Regular maintenance cleaning',
      features: ['Dusting', 'Vacuuming', 'Mopping', 'Bathroom cleaning', 'Kitchen cleaning']
    },
    { 
      id: 'deep', 
      name: 'Deep Clean', 
      price: 50, 
      description: 'Thorough detailed cleaning',
      features: ['Everything in Standard', 'Inside appliances', 'Baseboards', 'Light fixtures', 'Window sills']
    },
    { 
      id: 'move-in', 
      name: 'Move-in Ready', 
      price: 75, 
      description: 'Complete move-in preparation',
      features: ['Everything in Deep Clean', 'Inside cabinets', 'Interior windows', 'Sanitization', 'Move-in checklist']
    }
  ];

  const addOnServices = [
    { id: 'eco-friendly', name: 'Eco-Friendly Products', price: 15, icon: Heart },
    { id: 'inside-oven', name: 'Inside Oven Cleaning', price: 25, icon: Zap },
    { id: 'inside-fridge', name: 'Inside Refrigerator', price: 25, icon: Gift },
    { id: 'interior-windows', name: 'Interior Windows', price: 35, icon: Sparkles },
    { id: 'garage-cleaning', name: 'Garage Cleaning', price: 40, icon: Home },
    { id: 'basement-cleaning', name: 'Basement Cleaning', price: 30, icon: Building2 },
    { id: 'laundry-service', name: 'Laundry Wash & Fold', price: 20, icon: Users },
    { id: 'organization', name: 'Light Organization', price: 30, icon: CheckCircle }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      }
      default: {
        return '';
      }
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Real-time validation
    const error = validateField(field, value);
    setValidationErrors(prev => ({
      ...prev,
      [field]: error
    }));
  };

  // Calculate total price using centralized pricing service
  const calculateTotalPrice = (): number => {
    try {
      // Determine service type based on cleaning type
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Normalize service type to ensure compatibility
      const normalizedServiceType = ServiceTypeStandardizer.normalize(serviceType);

      // Convert property size to square footage estimate
      const sizeToSqft: Record<string, number> = {
        'studio': 500,
        'small': 800,
        'medium': 1200,
        'large': 1800,
        'xlarge': 2500
      };

      // Prepare pricing input
      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: sizeToSqft[formData.propertySize || 'medium'] || 1200,
        frequency: formData.frequency === 'bi-weekly' ? 'biweekly' :
                  formData.frequency === 'one-time' ? 'onetime' :
                  formData.frequency || 'onetime',
        addOns: formData.addOns || [],
        customOptions: {
          bedrooms: parseInt(formData.bedrooms || '0') || 0,
          bathrooms: parseInt(formData.bathrooms || '0') || 0,
          propertyType: formData.propertyType || 'house'
        }
      };

      // Calculate price using centralized service
      const pricingResult = calculatePrice(pricingInput);
      return Math.round(pricingResult.total);

    } catch (error) {
      console.error('Error calculating price:', error);
      // Fallback to a reasonable default
      return 150;
    }
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1: {
        return !!(formData.propertyType && formData.propertySize && formData.bedrooms && formData.bathrooms);
      }
      case 2: {
        return !!(formData.frequency && formData.cleaningType);
      }
      case 3: {
        return true; // Add-ons are optional
      }
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode', 'preferredDate', 'preferredTime'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default: {
        return false;
      }
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      // Prompt user to login
      alert('Please login to proceed with payment.');
      // Optionally, redirect to login page
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Determine correct service type based on cleaning type
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('residentialBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      // Determine correct service type based on cleaning type
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType, // Will be standardized below
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          size: formData.propertySize,
          bedrooms: formData.bedrooms,
          bathrooms: formData.bathrooms,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: formData.frequency,
          cleaningType: formData.cleaningType,
          addOns: formData.addOns || [],
          serviceSubType: serviceType, // Use the determined service type
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_regular_cleaning_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        console.error('Error code:', error.code);
        console.error('Error message:', error.message);
        console.error('Error details:', error.details);
        console.error('Error hint:', error.hint);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('residentialBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `RHC-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Regular House Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Regular House Cleaning',
            serviceType: 'Regular House Cleaning',
            status: 'confirmed',
            message: `Your ${formData.frequency?.toLowerCase() || 'regular'} cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `RHC-${Date.now()}`,
            confirmationNumber: `RHC-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Regular House Cleaning',
          bookingDetails: {
            id: `RHC-${Date.now()}`,
            type: 'Regular House Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Regular House Cleaning
            </h1>
            <p className="text-gray-600">Professional recurring cleaning service for your home.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                                         {currentStep > step.id ? <CheckCircle size={16} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property Details */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Tell us about your home</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <motion.button
                            key={type.id}
                            onClick={() => setFormData({ ...formData, propertyType: type.id })}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              formData.propertyType === type.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.propertyType === type.id 
                                  ? 'bg-emerald-100 text-emerald-800' 
                                  : 'bg-gray-100 text-gray-600'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Property Size */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Size</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertySizes.map((size) => (
                        <motion.button
                          key={size.id}
                          onClick={() => setFormData({ ...formData, propertySize: size.id })}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.propertySize === size.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <h3 className="font-semibold text-gray-900">{size.name}</h3>
                          <p className="text-sm text-emerald-800 font-medium">Starting at ${size.basePrice}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Bedrooms and Bathrooms */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Bedrooms</label>
                      <GlassmorphismSelect
                        options={[
                          { id: '1', name: '1 Bedroom' },
                          { id: '2', name: '2 Bedrooms' },
                          { id: '3', name: '3 Bedrooms' },
                          { id: '4', name: '4 Bedrooms' },
                          { id: '5+', name: '5+ Bedrooms' }
                        ]}
                        value={formData.bedrooms}
                        onChange={(value) => setFormData({ ...formData, bedrooms: value })}
                        placeholder="Select bedrooms"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-900 mb-3">Bathrooms</label>
                      <GlassmorphismSelect
                        options={[
                          { id: '1', name: '1 Bathroom' },
                          { id: '1.5', name: '1.5 Bathrooms' },
                          { id: '2', name: '2 Bathrooms' },
                          { id: '2.5', name: '2.5 Bathrooms' },
                          { id: '3', name: '3 Bathrooms' },
                          { id: '3.5', name: '3.5 Bathrooms' },
                          { id: '4+', name: '4+ Bathrooms' }
                        ]}
                        value={formData.bathrooms}
                        onChange={(value) => setFormData({ ...formData, bathrooms: value })}
                        placeholder="Select bathrooms"
                      />
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button 
                      onClick={() => setCurrentStep(2)} 
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Service Options */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Choose your cleaning plan</h2>
                  
                  {/* Cleaning Frequency */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">How often would you like service?</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {frequencies.map((freq) => (
                        <motion.button
                          key={freq.id}
                          onClick={() => setFormData({ ...formData, frequency: freq.id })}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          className={`p-4 rounded-xl border-2 text-left relative transition-all duration-200 ${
                            formData.frequency === freq.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          {freq.discount > 0 && (
                            <div className="absolute -top-2 -right-2 bg-gradient-to-r from-emerald-800 to-emerald-900 text-white text-xs px-3 py-1 rounded-full font-medium shadow-lg">
                              {Math.round(freq.discount * 100)}% OFF
                            </div>
                          )}
                          <h3 className="font-semibold text-gray-900 text-lg">{freq.name}</h3>
                          <p className="text-sm text-gray-600">{freq.description}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Cleaning Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">What type of cleaning do you need?</label>
                    <div className="space-y-4">
                      {cleaningTypes.map((type) => (
                        <motion.button
                          key={type.id}
                          onClick={() => setFormData({ ...formData, cleaningType: type.id })}
                          whileHover={{ scale: 1.01, y: -2 }}
                          whileTap={{ scale: 0.99 }}
                          className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.cleaningType === type.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{type.name}</h3>
                              <p className="text-sm text-gray-600">{type.description}</p>
                            </div>
                            {type.price > 0 && (
                              <span className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-sm">+${type.price}</span>
                            )}
                          </div>
                          <div className="grid grid-cols-2 gap-2">
                            {type.features.map((feature, index) => (
                              <div key={index} className="flex items-center gap-2 text-xs text-gray-700">
                                <CheckCircle className="w-3 h-3 text-emerald-600" />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)} 
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Add extra services</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((addon) => {
                      const IconComponent = addon.icon;
                      return (
                        <motion.button
                          key={addon.id}
                          onClick={() => handleAddOnToggle(addon.id)}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            (formData.addOns || []).includes(addon.id)
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100'
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center gap-4">
                            <div className={`p-2 rounded-lg transition-colors duration-200 ${
                              (formData.addOns || []).includes(addon.id)
                                ? 'bg-emerald-100 text-emerald-800'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              <IconComponent className="w-5 h-5" />
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between items-start">
                                <h3 className="font-semibold text-gray-900">{addon.name}</h3>
                                <span className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-sm">+${addon.price}</span>
                              </div>
                            </div>
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next <ArrowRight className="ml-2 h-5 w-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Scheduling */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact Information & Schedule</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={(e) => handleInputChange('city', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.city ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.city && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                    )}
                    
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.zipCode && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                    )}
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special Instructions" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Regular House Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          // Determine correct service type for payment
          let serviceType = 'residential_regular';
          if (formData.cleaningType === 'deep') {
            serviceType = 'residential_deep';
          } else if (formData.cleaningType === 'move') {
            serviceType = 'residential_move';
          }

          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernRegularCleaningForm;
