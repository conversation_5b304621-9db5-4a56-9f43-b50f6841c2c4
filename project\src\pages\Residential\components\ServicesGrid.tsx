import React from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, Truck, Construction, 
  Calendar, Brush, GlassWater, Waves, 
  Droplets, Flame, Sofa, Star
} from 'lucide-react';

const services = [
  {
    id: 'regular',
    icon: Home,
    title: 'Regular Cleaning',
    description: 'Scheduled cleaning to maintain a spotless home.',
    price: '$159',
    popular: true,
    features: ['Dusting & vacuuming', 'Kitchen & bathroom cleaning', 'Trash removal', 'Flexible scheduling'],
    color: 'from-blue-500 to-blue-600',
    glowColor: 'rgba(59, 130, 246, 0.3)'
  },
  {
    id: 'deep',
    icon: Sparkles,
    title: 'Deep Cleaning',
    description: 'A thorough, top-to-bottom home refresh.',
    price: '$299',
    features: ['Detailed cleaning', 'Inside appliances', 'Baseboards & windows', 'Cabinet cleaning'],
    color: 'from-purple-500 to-purple-600',
    glowColor: 'rgba(168, 85, 247, 0.3)'
  },
  {
    id: 'move',
    icon: Truck,
    title: 'Move In/Out Cleaning',
    description: 'Ensure a clean slate for moving day.',
    price: '$349',
    features: ['Cabinet cleaning', 'Appliance cleaning', 'Window cleaning'],
    color: 'from-green-400 to-green-600',
    glowColor: 'rgba(34, 197, 94, 0.4)',
    path: '/residential/moveout'
  },
  {
    id: 'construction',
    icon: Construction,
    title: 'Post-Construction',
    description: 'Cleaning up the dust and debris after renovations.',
    price: '$499',
    features: ['Debris removal', 'Dust control', 'Surface cleaning'],
    color: 'from-orange-400 to-orange-600',
    glowColor: 'rgba(251, 146, 60, 0.4)',
    path: '/residential/postconstruction'
  },
  {
    id: 'event',
    icon: Calendar,
    title: 'Event Cleaning',
    description: 'Pre- and post-event support to make hosting easy.',
    price: '$249',
    features: ['Setup assistance', 'Post-event cleanup', 'Quick turnaround'],
    color: 'from-pink-400 to-pink-600',
    glowColor: 'rgba(236, 72, 153, 0.4)',
    path: '/residential/event'
  },
  {
    id: 'carpet',
    icon: Brush,
    title: 'Carpet Cleaning',
    description: 'Deep extraction to remove stains and allergens.',
    price: '$199',
    features: ['Steam cleaning', 'Stain treatment', 'Odor removal', 'Fast drying'],
    color: 'from-green-500 to-green-600',
    glowColor: 'rgba(34, 197, 94, 0.3)'
  },
  {
    id: 'upholstery',
    icon: Sofa,
    title: 'Upholstery Cleaning',
    description: 'Deep cleaning for furniture, sofas, and fabric care',
    price: 'Starting $179',
    features: ['Fabric protection', 'Stain removal', 'Odor elimination', 'Color restoration'],
    color: 'from-indigo-500 to-indigo-600',
    glowColor: 'rgba(99, 102, 241, 0.3)'
  },
  {
    id: 'window',
    icon: GlassWater,
    title: 'Window Cleaning',
    description: 'Interior and exterior window cleaning for crystal clear views',
    price: 'Starting $149',
    features: ['Interior & exterior', 'Screen cleaning', 'Sill wiping', 'Streak-free finish'],
    color: 'from-cyan-500 to-cyan-600',
    glowColor: 'rgba(6, 182, 212, 0.3)'
  },
  {
    id: 'pressure',
    icon: Waves,
    title: 'Pressure Washing',
    description: 'Exterior surface cleaning for driveways, decks, and siding',
    price: 'Starting $249',
    features: ['Driveway cleaning', 'Deck restoration', 'Siding wash', 'Mold removal'],
    color: 'from-teal-500 to-teal-600',
    glowColor: 'rgba(20, 184, 166, 0.3)'
  },
  {
    id: 'sanitization',
    icon: Droplets,
    title: 'Sanitization & Disinfection',
    description: 'Deep sanitization services',
    features: ['Surface disinfection', 'High-touch areas', 'Odor elimination'],
    color: 'from-emerald-400 to-emerald-600',
    glowColor: 'rgba(16, 185, 129, 0.4)',
    path: '/residential/sanitization'
  },
  {
    id: 'pool',
    icon: Droplets,
    title: 'Pool Cleaning',
    description: 'Subscription ONLY service',
    features: ['Water testing', 'Chemical balancing', 'Filter cleaning'],
    color: 'from-blue-300 to-blue-500',
    glowColor: 'rgba(96, 165, 250, 0.4)',
    path: '/residential/pool'
  },
  {
    id: 'chimney',
    icon: Flame,
    title: 'Chimney Cleaning',
    description: 'Subscription or one-time service',
    features: ['Soot removal', 'Creosote removal', 'Safety inspection'],
    color: 'from-red-400 to-red-600',
    glowColor: 'rgba(248, 113, 113, 0.4)',
    path: '/residential/chimney'
  }
];

export function ServicesGrid() {
  const navigate = useNavigate();

  const handleServiceClick = (serviceId: string) => {
    // Find the service to check if it has a custom path
    const service = services.find(s => s.id === serviceId);
    const targetPath = service?.path || `/residential/${serviceId}`;
    navigate(targetPath);
  };

  return (
    <section className="py-24 sm:py-32" id="residential-services">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-black mb-4">Services Tailored For You</h2>
          <p className="text-lg md:text-xl text-black/70 max-w-3xl mx-auto">
            From routine upkeep to specialized deep cleans, we offer a service for every need.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const Icon = service.icon;
            
            return (
              <motion.div
                key={service.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                className="relative p-8 rounded-2xl border border-gray-200 h-full flex flex-col group cursor-pointer bg-white shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
                onClick={() => handleServiceClick(service.id)}
              >
                {service.popular && (
                  <div className="absolute top-0 right-8 -mt-4">
                    <div className="px-4 py-1.5 rounded-full text-sm font-semibold text-white bg-emerald-500 shadow-md">
                      Popular
                    </div>
                      </div>
                )}

                <div className="flex items-center justify-center w-16 h-16 bg-emerald-500 rounded-2xl mb-6 shadow-md group-hover:bg-emerald-600 transition-colors">
                  <Icon 
                    className="w-8 h-8" 
                    color="#ffffff"
                    fill="none"
                    stroke="#ffffff"
                    strokeWidth="2"
                  />
                    </div>

                <div className="flex-grow">
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-emerald-700 transition-colors">{service.title}</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">{service.description}</p>
                      </div>
                
                <div className="mt-auto flex justify-between items-center">
                  <span className="text-lg font-semibold text-emerald-600">{service.price}</span>
                  <div className="text-gray-500 group-hover:text-emerald-600 transition-colors flex items-center">
                    <span className="mr-2">Book Now</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
