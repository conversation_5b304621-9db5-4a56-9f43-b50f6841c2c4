import React, { useState } from 'react';
import { Star, ChevronLeft, ChevronRight } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const reviews = [
  { author: "<PERSON>", content: "Exceptional service! They transformed our office space completely. The team was professional, efficient, and thorough." },
  { author: "<PERSON>", content: "Outstanding medical facility cleaning. Their attention to sanitization protocols is impressive." },
  { author: "<PERSON>", content: "Best commercial cleaning service in NYC! Professional and reliable every time." },
  { author: "<PERSON>", content: "Great service results. Very satisfied with their work and attention to detail." },
  { author: "<PERSON>", content: "Fantastic service! The booking process was seamless and the team was punctual." },
  { author: "<PERSON>", content: "They've been cleaning our retail spaces for years. Consistently excellent service!" },
];

export function ReviewCarousel() {
  const [index, setIndex] = useState(0);

  const paginate = (newDirection: number) => {
    setIndex(prevIndex => (prevIndex + newDirection + reviews.length) % reviews.length);
  };

  return (
    <section className="py-16 sm:py-20 lg:py-24 overflow-hidden bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12 lg:mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">What Our Clients Say</h2>
          <div className="w-16 h-1 bg-emerald-800 mx-auto mb-6 rounded-full" />
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            We're proud to have earned the trust of businesses and homeowners across the country.
          </p>
        </motion.div>

        <div className="relative h-72 sm:h-80 lg:h-72 flex items-center justify-center">
          <AnimatePresence custom={index}>
          <motion.div 
              key={index}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
              className="absolute w-full max-w-2xl text-center px-4 sm:px-8"
          >
              <div className="flex justify-center mb-3 sm:mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 sm:w-5 sm:h-5 text-emerald-500 fill-current" />
                ))}
              </div>
              <div className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 sm:p-10 shadow-lg border-2 border-emerald-200 hover:border-emerald-300 transition-all duration-300 hover:shadow-xl hover:shadow-emerald-200/30 relative overflow-hidden group">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-emerald-600 via-emerald-700 to-emerald-600 opacity-70" />
                <p className="text-lg sm:text-xl italic text-gray-700 mb-6 sm:mb-8 leading-relaxed">"{reviews[index].content}"</p>
                <div className="flex items-center justify-center gap-2">
                  <div className="w-8 h-0.5 bg-emerald-600 rounded-full" />
                  <p className="font-semibold text-emerald-800 text-base sm:text-lg">{reviews[index].author}</p>
                  <div className="w-8 h-0.5 bg-emerald-600 rounded-full" />
                </div>
              </div>
              </motion.div>
            </AnimatePresence>
          </div>

        <div className="flex items-center justify-center mt-8 sm:mt-10 gap-4 sm:gap-6">
          <motion.button
            onClick={() => paginate(-1)}
            className="p-2 sm:p-3 rounded-full bg-emerald-100 border border-emerald-200 transition-all duration-300 hover:bg-emerald-200 hover:border-emerald-300 hover:shadow-lg hover:shadow-emerald-200/50"
            whileHover={{ scale: 1.1, rotate: -5 }}
            whileTap={{ scale: 0.9 }}
            >
            <ChevronLeft className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-800 hover:text-emerald-900 transition-colors" />
          </motion.button>
          <motion.button
            onClick={() => paginate(1)}
            className="p-2 sm:p-3 rounded-full bg-emerald-100 border border-emerald-200 transition-all duration-300 hover:bg-emerald-200 hover:border-emerald-300 hover:shadow-lg hover:shadow-emerald-200/50"
            whileHover={{ scale: 1.1, rotate: 5 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronRight className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-800 hover:text-emerald-900 transition-colors" />
          </motion.button>
        </div>
      </div>
    </section>
  );
}
