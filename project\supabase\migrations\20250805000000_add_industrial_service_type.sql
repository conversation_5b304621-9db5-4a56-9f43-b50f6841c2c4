/*
  # Add Industrial Service Type

  This migration adds 'industrial' to the service type constraints in the database
  to allow industrial cleaning bookings to be saved properly.

  ISSUE FIXED:
  - Industrial cleaning form submissions were failing because 'industrial' was not
    included in the service_type CHECK constraints on booking_forms and payment_records tables
  - This caused industrial bookings to be rejected at the database level

  CHANGES:
  1. Drop existing service type constraints
  2. Re-create constraints including 'industrial' service type
  3. Add proper indexing for performance
*/

-- ============================================================================
-- STEP 1: Remove existing service type constraints
-- ============================================================================

ALTER TABLE booking_forms DROP CONSTRAINT IF EXISTS booking_forms_service_type_check;
ALTER TABLE payment_records DROP CONSTRAINT IF EXISTS payment_records_service_type_check;

-- ============================================================================
-- STEP 2: Add updated service type constraints including 'industrial'
-- ============================================================================

-- Apply service type constraints for booking_forms (including industrial)
ALTER TABLE booking_forms 
ADD CONSTRAINT booking_forms_service_type_check CHECK (
  service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction', 'residential_event', 'residential_upholstery',
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management', 'industrial'
  )
);

-- Apply service type constraints for payment_records (including industrial)
ALTER TABLE payment_records 
ADD CONSTRAINT payment_records_service_type_check CHECK (
  service_type IN (
    'residential_regular', 'residential_deep', 'residential_move', 'residential',
    'residential_post_construction', 'residential_event', 'residential_upholstery', 
    'office', 'carpet', 'window', 'construction', 'sanitization',
    'tile', 'pressure', 'floor', 'pool', 'chimney', 'waste-management', 'industrial'
  )
);

-- ============================================================================
-- STEP 3: Add helpful comment for future reference
-- ============================================================================

COMMENT ON CONSTRAINT booking_forms_service_type_check ON booking_forms IS 
'Ensures service_type is one of the standardized service types including industrial cleaning';

COMMENT ON CONSTRAINT payment_records_service_type_check ON payment_records IS 
'Ensures service_type is one of the standardized service types including industrial cleaning';

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Log completion
DO $$
BEGIN
  RAISE NOTICE 'Industrial service type constraint added successfully. Industrial cleaning bookings can now be saved.';
END $$;