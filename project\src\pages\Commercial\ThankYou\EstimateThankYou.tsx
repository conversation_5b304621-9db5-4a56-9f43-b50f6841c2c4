import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, Home, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Header } from '../../../components/layout/Header';
import { Footer } from '../../../components/layout/Footer';
import { Button } from '../../../components/ui/Button';

export function EstimateThankYou() {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header />
      <main className="flex-grow flex items-center justify-center py-20 px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-lg w-full bg-white border border-gray-200 rounded-2xl shadow-lg p-8 text-center"
        >
          <CheckCircle className="w-16 h-16 text-emerald-600 mx-auto mb-6" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Estimate Requested!</h1>
          <p className="text-gray-700 mb-6">
            Thank you for your interest. Our team will contact you shortly with a personalised estimate.
          </p>
          <Button
            className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 inline-flex items-center gap-2"
            onClick={() => navigate('/')}
          >
            Return Home <ArrowRight className="w-5 h-5" />
          </Button>
        </motion.div>
      </main>
      <Footer />
    </div>
  );
}
