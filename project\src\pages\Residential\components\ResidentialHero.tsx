
import { motion } from 'framer-motion';
import { 
  ArrowDown, <PERSON>, Star, Wind
} from 'lucide-react';

export function ResidentialHero() {

  const scrollToServices = () => {
    const servicesSection = document.getElementById('services-section');
    if (servicesSection) {
      servicesSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <>
      <section className="relative min-h-[85vh] flex items-center justify-center pt-20 text-white overflow-hidden"
        style={{
          backgroundImage: 'url("https://images.unsplash.com/photo-1585412727339-54e4bae3bbf9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >


        {/* Main Content */}
        <div className="relative z-10 w-full max-w-4xl mx-auto px-4 text-center">
            <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-5xl md:text-7xl font-bold leading-tight mb-6"
            style={{ color: '#ffffff', textShadow: '0 2px 8px rgba(0,0,0,0.7), 0 1px 3px rgba(0,0,0,0.5)' }}
              >
            Effortless Clean,
            <br />
            Impeccable Home.
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            className="text-lg md:text-xl max-w-2xl mx-auto mb-10"
            style={{ color: '#ffffff', textShadow: '0 2px 6px rgba(0,0,0,0.6), 0 1px 3px rgba(0,0,0,0.4)' }}
            >
            Discover a new standard of clean with our professional, eco-friendly residential services.
            </motion.p>

            <motion.div
            initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: 'easeOut' }}
            className="flex items-center justify-center"
            >
            <motion.button
              onClick={scrollToServices}
              whileHover={{ 
                scale: 1.05, 
                y: -3,
                boxShadow: '0 10px 30px rgba(0,0,0,0.4)',
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 md:px-10 md:py-5 text-base md:text-lg font-bold rounded-xl transition-all duration-300 flex items-center gap-3 bg-emerald-600 text-white shadow-lg hover:shadow-2xl relative overflow-hidden whitespace-nowrap hover:bg-emerald-700"
              style={{
                boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
              }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0"
                whileHover={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
              <span className="relative z-10 whitespace-nowrap" style={{ color: '#ffffff' }}>Book Now</span>
              <motion.div
                animate={{ y: [0, 3, 0] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
                className="relative z-10"
              >
                <ArrowDown 
                  className="w-5 h-5" 
                  color="#ffffff" 
                  fill="none" 
                  stroke="#ffffff" 
                  strokeWidth="2"
                />
              </motion.div>
            </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: 'easeOut' }}
            className="flex justify-center items-center gap-8 mt-16"
            >
              <div className="flex items-center gap-2 text-white">
                <Shield 
                  className="w-5 h-5" 
                  color="#ffffff" 
                  fill="none" 
                  stroke="#ffffff" 
                  strokeWidth="2"
                />
                <span className="text-sm font-medium" style={{ color: '#ffffff', textShadow: '0 1px 4px rgba(0,0,0,0.5), 0 1px 2px rgba(0,0,0,0.3)' }}>Insured</span>
              </div>
              <div className="flex items-center gap-2 text-white">
                <Star 
                  className="w-5 h-5" 
                  color="#ffffff" 
                  fill="#ffffff" 
                  stroke="#ffffff" 
                  strokeWidth="2"
                />
                <span className="text-sm font-medium" style={{ color: '#ffffff', textShadow: '0 1px 4px rgba(0,0,0,0.5), 0 1px 2px rgba(0,0,0,0.3)' }}>5-Star Rated</span>
              </div>
              <div className="flex items-center gap-2 text-white">
                <Wind 
                  className="w-5 h-5" 
                  color="#ffffff" 
                  fill="none" 
                  stroke="#ffffff" 
                  strokeWidth="2"
                />
                <span className="text-sm font-medium" style={{ color: '#ffffff', textShadow: '0 1px 4px rgba(0,0,0,0.5), 0 1px 2px rgba(0,0,0,0.3)' }}>Eco-Friendly</span>
              </div>
            </motion.div>
        </div>
      </section>


    </>
  );
}
