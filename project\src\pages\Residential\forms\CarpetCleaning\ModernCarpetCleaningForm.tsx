import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Home, Sparkles, User, CheckCircle, ArrowRight,
  Building2, Castle, Warehouse, Sofa, Brush,
  Mail, Phone, MapPin, Gift, Zap, Shield, Star,
  Wind, Layers, Package, Clock
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import GlassmorphismSelect from '../../../../components/ui/GlassmorphismSelect';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { supabase } from '../../../../lib/supabase/client';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';

interface FormData {
  propertyType: string;
  numberOfRooms: string;
  carpetType: string;
  cleaningIntensity: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const ModernCarpetCleaningForm: React.FC = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<FormData>>({
    propertyType: '',
    numberOfRooms: '',
    carpetType: '',
    cleaningIntensity: '',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });
  
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const { user } = useAuth();

  // Save form data to localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('carpetCleaningFormData');
    if (savedData) {
      setFormData(JSON.parse(savedData));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('carpetCleaningFormData', JSON.stringify(formData));
  }, [formData]);

  const steps = [
    { id: 1, name: 'Property & Rooms' },
    { id: 2, name: 'Carpet Details' },
    { id: 3, name: 'Add-ons' },
    { id: 4, name: 'Contact & Schedule' },
  ];

  const propertyTypes = [
    { 
      id: 'apartment', 
      name: 'Apartment/Condo', 
      icon: Building2, 
      description: 'Studio to 3BR units',
      multiplier: 1.0
    },
    { 
      id: 'house', 
      name: 'Single Family House', 
      icon: Home, 
      description: '1-2 story homes',
      multiplier: 1.1
    },
    { 
      id: 'townhouse', 
      name: 'Townhouse', 
      icon: Castle, 
      description: 'Multi-level attached',
      multiplier: 1.05
    },
    { 
      id: 'large-home', 
      name: 'Large Home/Estate', 
      icon: Warehouse, 
      description: '3+ stories, 4+ bedrooms',
      multiplier: 1.2
    }
  ];

  const roomOptions = [
    { 
      id: '1-2', 
      name: '1-2 Rooms', 
      icon: Sofa,
      description: 'Living room, bedroom, or office',
      details: '1-2 hours service',
      basePrice: 89
    },
    { 
      id: '3-4', 
      name: '3-4 Rooms', 
      icon: Home,
      description: 'Most common for apartments/homes',
      details: '2-3 hours service',
      basePrice: 159,
      popular: true
    },
    { 
      id: '5-6', 
      name: '5-6 Rooms', 
      icon: Building2,
      description: 'Larger homes and common areas',
      details: '3-4 hours service',
      basePrice: 229
    },
    { 
      id: '7+', 
      name: '7+ Rooms', 
      icon: Warehouse,
      description: 'Large homes, whole house cleaning',
      details: '4+ hours service',
      basePrice: 289
    }
  ];

  const carpetTypes = [
    { 
      id: 'standard', 
      name: 'Standard Carpet', 
      description: 'Most common household carpet',
      details: 'Cut pile, loop pile, polyester',
      priceMultiplier: 1.0
    },
    { 
      id: 'berber', 
      name: 'Berber Carpet', 
      description: 'Loop pile construction',
      details: 'Requires special care (+10%)',
      priceMultiplier: 1.1
    },
    { 
      id: 'shag', 
      name: 'Shag/High Pile', 
      description: 'Long fiber construction',
      details: 'Deep cleaning required (+20%)',
      priceMultiplier: 1.2
    },
    { 
      id: 'wool', 
      name: 'Wool Carpet', 
      description: 'Natural fiber carpet',
      details: 'Premium care (+25%)',
      priceMultiplier: 1.25
    }
  ];

  const cleaningIntensityOptions = [
    { 
      id: 'standard', 
      name: 'Standard Clean', 
      price: 0, 
      description: 'Regular maintenance cleaning',
      features: ['Hot water extraction', 'Vacuum preparation', 'Spot treatment', 'Fast drying']
    },
    { 
      id: 'deep', 
      name: 'Deep Clean', 
      price: 30, 
      description: 'Thorough restoration cleaning',
      features: ['Pre-treatment', 'Hot water extraction', 'Deep spot treatment', 'Rinse extraction', 'Grooming']
    },
    { 
      id: 'restoration', 
      name: 'Restoration Clean', 
      price: 50, 
      description: 'Heavy soil & stain removal',
      features: ['Everything in Deep Clean', 'Multiple passes', 'Heavy stain treatment', 'Sanitization', 'Extended drying']
    }
  ];

  const addOnServices = [
    { id: 'stain-protection', name: 'Stain Protection Shield', price: 59, icon: Shield },
    { id: 'deep-deodorizing', name: 'Deep Deodorizing', price: 49, icon: Wind },
    { id: 'upholstery-cleaning', name: 'Upholstery Cleaning', price: 79, icon: Sofa },
    { id: 'pet-treatment', name: 'Pet Odor & Stain Treatment', price: 69, icon: Star },
    { id: 'rug-cleaning', name: 'Area Rug Cleaning', price: 39, icon: Layers },
    { id: 'mattress-cleaning', name: 'Mattress Cleaning', price: 89, icon: Package },
    { id: 'scotchgard', name: 'Scotchgard Protection', price: 45, icon: Sparkles },
    { id: 'anti-allergen', name: 'Anti-Allergen Treatment', price: 55, icon: Zap }
  ];

  const timeSlots = [
    { id: 'morning', name: 'Morning (8AM - 12PM)' },
    { id: 'afternoon', name: 'Afternoon (1PM - 5PM)' },
    { id: 'evening', name: 'Evening (5PM - 9PM)' }
  ];

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName': {
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      }
      case 'email': {
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      }
      case 'phone': {
        return !validatePhone(value) ? 'Please enter a valid phone number' : '';
      }
      case 'address': {
        return value.length < 5 ? 'Please enter a complete address' : '';
      }
      case 'city': {
        return value.length < 2 ? 'Please enter a valid city' : '';
      }
      case 'zipCode': {
        return !validateZipCode(value) ? 'Please enter a valid ZIP code' : '';
      }
      default:
        return '';
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
    
    // Validate field on change
    const error = validateField(field, value);
    if (error) {
      setValidationErrors(prev => ({ ...prev, [field]: error }));
    }
  };

  const calculateTotalPrice = (): number => {
    const selectedRooms = roomOptions.find(room => room.id === formData.numberOfRooms);
    const selectedProperty = propertyTypes.find(type => type.id === formData.propertyType);
    const selectedCarpet = carpetTypes.find(type => type.id === formData.carpetType);
    const selectedIntensity = cleaningIntensityOptions.find(intensity => intensity.id === formData.cleaningIntensity);
    
    let basePrice = selectedRooms?.basePrice || 159;
    basePrice *= selectedProperty?.multiplier || 1.0;
    basePrice *= selectedCarpet?.priceMultiplier || 1.0;
    basePrice += selectedIntensity?.price || 0;
    
    const addOnTotal = (formData.addOns || []).reduce((total, addOnId) => {
      const addOn = addOnServices.find(service => service.id === addOnId);
      return total + (addOn?.price || 0);
    }, 0);

    return Math.round(basePrice + addOnTotal);
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.numberOfRooms);
      case 2:
        return !!(formData.carpetType && formData.cleaningIntensity);
      case 3:
        return true; // Add-ons are optional
      case 4:
        return !!(
          formData.firstName && 
          formData.lastName && 
          formData.email && 
          formData.phone && 
          formData.address && 
          formData.city && 
          formData.zipCode && 
          formData.preferredDate && 
          formData.preferredTime &&
          !Object.keys(validationErrors).length
        );
      default:
        return false;
    }
  };

  // Handle form submission - now shows payment modal
  const handleSubmit = async () => {
    if (!isStepValid(4)) return;
    
    if (!user) {
      alert('Please login to proceed with payment.');
      navigate('/auth/login');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Use 'carpet-cleaning' service type
      const serviceType = 'carpet-cleaning';

      // Standardize the form data before saving
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        cleaningType: 'carpet',
        frequency: 'one-time', // Carpet cleaning is typically one-time
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence
      localStorage.setItem('carpetCleaningBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      const serviceType = 'carpet-cleaning';

      // Prepare standardized booking data for database
      const rawBookingData = {
        user_id: user?.id,
        service_type: serviceType,
        status: 'pending',
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        property_details: {
          type: formData.propertyType,
          address: formData.address,
          city: formData.city,
          zipCode: formData.zipCode
        },
        service_details: {
          frequency: 'one-time',
          cleaningType: 'carpet',
          numberOfRooms: formData.numberOfRooms,
          carpetType: formData.carpetType,
          cleaningIntensity: formData.cleaningIntensity,
          addOns: formData.addOns || [],
          serviceSubType: serviceType,
          totalPrice: calculateTotalPrice(),
          actualServiceType: serviceType,
          specialInstructions: formData.specialInstructions || '',
          submittedAt: new Date().toISOString(),
          source: 'modern_carpet_cleaning_form'
        },
        schedule: {
          preferredDate: formData.preferredDate,
          preferredTime: formData.preferredTime
        }
      };

      // Standardize the booking data for database insertion
      const bookingData = ServiceTypeStandardizer.standardizeBookingData(rawBookingData);

      console.log('Attempting to save carpet cleaning booking with data:', bookingData);

      // Save to database
      const { data: savedBooking, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Detailed error saving booking:', error);
        throw new Error(`Failed to save booking to database: ${error.message}`);
      }

      console.log('Carpet cleaning booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved
      localStorage.removeItem('carpetCleaningBookingData');
      
      // Navigate to Thank You page with booking data
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: savedBooking.id,
            confirmationNumber: `CC-${savedBooking.id}`,
            emailSent: true
          },
          paymentStatus: 'paid',
          serviceType: 'Carpet Cleaning',
          bookingDetails: {
            id: savedBooking.id,
            type: 'Carpet Cleaning',
            serviceType: 'Carpet Cleaning',
            status: 'confirmed',
            message: `Your carpet cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          }
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
      // Still navigate to Thank You page but with processing status
      navigate('/thank-you', { 
        state: { 
          formData: {
            ...formData,
            totalPrice: calculateTotalPrice(),
            bookingId: `CC-${Date.now()}`,
            confirmationNumber: `CC-${Date.now()}`,
            emailSent: false
          },
          paymentStatus: 'paid',
          serviceType: 'Carpet Cleaning',
          bookingDetails: {
            id: `CC-${Date.now()}`,
            type: 'Carpet Cleaning',
            status: 'processing',
            message: 'Payment completed! Your booking is being processed and will appear shortly.'
          }
        }
      });
    }
  };

  const handleAddOnToggle = (addOnId: string) => {
    const currentAddOns = formData.addOns || [];
    if (currentAddOns.includes(addOnId)) {
      setFormData({
        ...formData,
        addOns: currentAddOns.filter(id => id !== addOnId)
      });
    } else {
      setFormData({
        ...formData,
        addOns: [...currentAddOns, addOnId]
      });
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen w-full flex items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} className="text-center mb-8">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              Professional Carpet Cleaning
            </h1>
            <p className="text-gray-600">Deep extraction cleaning to restore your carpets to like-new condition.</p>
          </motion.div>

          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200 ${currentStep >= step.id ? 'bg-emerald-800 text-white shadow-md' : 'bg-white border-2 border-gray-200 text-gray-600'}`}>
                    {currentStep > step.id ? <CheckCircle size={16} className="text-white" /> : step.id}
                  </div>
                  <span className={`ml-2 text-sm ${currentStep >= step.id ? 'text-gray-900 font-medium' : 'text-gray-500'} hidden sm:block`}>
                    {step.name}
                  </span>
                </div>
              ))}
            </div>
            <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
              <motion.div className="bg-emerald-800 h-full rounded-full" animate={{ width: `${(currentStep / steps.length) * 100}%` }} />
            </div>
          </div>

          <motion.div className="bg-white border border-gray-200 rounded-2xl p-6 sm:p-8 shadow-lg hover:shadow-xl transition-all duration-200" initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }}>
            <AnimatePresence mode="wait">
              {/* Step 1: Property & Rooms */}
              {currentStep === 1 && (
                <motion.div key="step1">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Property & Room Details</h2>
                  
                  {/* Property Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Property Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {propertyTypes.map((type) => {
                        const IconComponent = type.icon;
                        return (
                          <motion.button
                            key={type.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, propertyType: type.id })}
                            className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                              formData.propertyType === type.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.propertyType === type.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{type.name}</h3>
                                <p className="text-sm text-gray-600">{type.description}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Number of Rooms */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Number of Rooms to Clean</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {roomOptions.map((room) => {
                        const IconComponent = room.icon;
                        return (
                          <motion.button
                            key={room.id}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            onClick={() => setFormData({ ...formData, numberOfRooms: room.id })}
                            className={`p-4 rounded-xl border-2 text-left relative transition-all duration-200 ${
                              formData.numberOfRooms === room.id 
                                ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                                : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                            }`}
                          >
                            {room.popular && (
                              <div className="absolute -top-2 -right-2 bg-emerald-600 text-white text-xs px-2 py-1 rounded-full">
                                Popular
                              </div>
                            )}
                            <div className="flex items-center gap-4">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                formData.numberOfRooms === room.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-6 h-6" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900">{room.name}</h3>
                                <p className="text-sm text-gray-600">{room.description}</p>
                                <p className="text-xs text-gray-500">{room.details}</p>
                                <p className="text-sm text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">Starting at ${room.basePrice}</p>
                              </div>
                            </div>
                          </motion.button>
                        );
                      })}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => window.history.back()}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(2)}
                      disabled={!isStepValid(1)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Carpet Details
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 2: Carpet Details */}
              {currentStep === 2 && (
                <motion.div key="step2">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Carpet Type & Cleaning Level</h2>
                  
                  {/* Carpet Type */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Carpet Type</label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {carpetTypes.map((carpet) => (
                        <motion.button
                          key={carpet.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, carpetType: carpet.id })}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.carpetType === carpet.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <h3 className="font-semibold text-gray-900">{carpet.name}</h3>
                          <p className="text-sm text-gray-600">{carpet.description}</p>
                          <p className="text-xs text-gray-500">{carpet.details}</p>
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  {/* Cleaning Intensity */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-900 mb-4">Cleaning Level</label>
                    <div className="grid grid-cols-1 gap-4">
                      {cleaningIntensityOptions.map((intensity) => (
                        <motion.button
                          key={intensity.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => setFormData({ ...formData, cleaningIntensity: intensity.id })}
                          className={`p-6 rounded-xl border-2 text-left transition-all duration-200 ${
                            formData.cleaningIntensity === intensity.id 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-3">
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">{intensity.name}</h3>
                              <p className="text-gray-600">{intensity.description}</p>
                              {intensity.price > 0 && (
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg inline-block mt-1">+${intensity.price}</p>
                              )}
                            </div>
                            <div className={`p-2 rounded-lg transition-colors duration-200 ${
                              formData.cleaningIntensity === intensity.id ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                            }`}>
                              <Brush className="w-6 h-6" />
                            </div>
                          </div>
                          <ul className="text-sm text-gray-700 space-y-1">
                            {intensity.features.map((feature, index) => (
                              <li key={index} className="flex items-center">
                                <CheckCircle className="w-4 h-4 text-emerald-600 mr-2" />
                                {feature}
                              </li>
                            ))}
                          </ul>
                        </motion.button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(1)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(3)}
                      disabled={!isStepValid(2)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next: Add-ons
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 3: Add-ons */}
              {currentStep === 3 && (
                <motion.div key="step3">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Enhance your carpet cleaning</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    {addOnServices.map((service) => {
                      const IconComponent = service.icon;
                      const isSelected = (formData.addOns || []).includes(service.id);
                      
                      return (
                        <motion.button
                          key={service.id}
                          whileHover={{ scale: 1.02, y: -2 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleAddOnToggle(service.id)}
                          className={`p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                            isSelected 
                              ? 'bg-emerald-50 border-emerald-400 shadow-md shadow-emerald-100' 
                              : 'bg-white border-gray-200 hover:border-emerald-200 hover:bg-gray-50 shadow-sm hover:shadow-md'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg transition-colors duration-200 ${
                                isSelected ? 'bg-emerald-100 text-emerald-800' : 'bg-emerald-50 text-emerald-700'
                              }`}>
                                <IconComponent className="w-5 h-5" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-sm">{service.name}</h3>
                                <p className="text-emerald-800 font-bold bg-emerald-100 px-2 py-1 rounded-lg text-xs inline-block">${service.price}</p>
                              </div>
                            </div>
                            {isSelected && <CheckCircle className="w-5 h-5 text-emerald-600" />}
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>

                  {/* Price Summary */}
                  <div className="bg-gray-50 border border-gray-200 rounded-xl p-4 mb-6 shadow-sm">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-900 font-semibold">Estimated Total:</span>
                      <span className="text-2xl font-bold text-emerald-800">${calculateTotalPrice()}</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(2)}>Back</Button>
                    <Button 
                      onClick={() => setCurrentStep(4)}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105"
                    >
                      Next: Contact & Schedule
                      <ArrowRight className="ml-2 w-5 h-5 text-white" />
                    </Button>
                  </div>
                </motion.div>
              )}

              {/* Step 4: Contact & Schedule */}
              {currentStep === 4 && (
                <motion.div key="step4">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Contact & Scheduling</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="First Name" 
                        value={formData.firstName || ''} 
                        onChange={(e) => handleInputChange('firstName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.firstName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.firstName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <User className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Last Name" 
                        value={formData.lastName || ''} 
                        onChange={(e) => handleInputChange('lastName', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.lastName ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.lastName && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.lastName}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Mail className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="email" 
                        placeholder="Email" 
                        value={formData.email || ''} 
                        onChange={(e) => handleInputChange('email', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.email ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.email && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.email}</p>
                      )}
                    </div>
                    
                    <div className="relative">
                      <Phone className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="tel" 
                        placeholder="Phone" 
                        value={formData.phone || ''} 
                        onChange={(e) => handleInputChange('phone', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.phone ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.phone && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.phone}</p>
                      )}
                    </div>
                    
                    <div className="relative sm:col-span-2">
                      <MapPin className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400" size={20} />
                      <input 
                        type="text" 
                        placeholder="Property Address" 
                        value={formData.address || ''} 
                        onChange={(e) => handleInputChange('address', e.target.value)} 
                        className={`w-full bg-white p-3 pl-12 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                          validationErrors.address ? 'border-red-400' : 'border-gray-200'
                        }`} 
                      />
                      {validationErrors.address && (
                        <p className="text-red-400 text-xs mt-1">{validationErrors.address}</p>
                      )}
                    </div>
                    
                    <input 
                      type="text" 
                      placeholder="City" 
                      value={formData.city || ''} 
                      onChange={(e) => handleInputChange('city', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.city ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.city && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.city}</p>
                    )}
                    
                    <input 
                      type="text" 
                      placeholder="ZIP Code" 
                      value={formData.zipCode || ''} 
                      onChange={(e) => handleInputChange('zipCode', e.target.value)} 
                      className={`w-full bg-white p-3 rounded-xl border text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md ${
                        validationErrors.zipCode ? 'border-red-400' : 'border-gray-200'
                      }`} 
                    />
                    {validationErrors.zipCode && (
                      <p className="text-red-400 text-xs mt-1">{validationErrors.zipCode}</p>
                    )}
                    
                    <input 
                      type="date" 
                      value={formData.preferredDate || ''} 
                      onChange={(e) => setFormData({ ...formData, preferredDate: e.target.value })} 
                      min={new Date().toISOString().split('T')[0]}
                      className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md" 
                    />
                    
                    <GlassmorphismSelect
                      options={timeSlots}
                      value={formData.preferredTime}
                      onChange={(value) => setFormData({ ...formData, preferredTime: value })}
                      placeholder="Select Time"
                    />
                  </div>
                  
                  <textarea 
                    placeholder="Special instructions, carpet conditions, or stain details" 
                    value={formData.specialInstructions || ''} 
                    onChange={(e) => setFormData({ ...formData, specialInstructions: e.target.value })} 
                    className="w-full bg-white p-3 rounded-xl border border-gray-200 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all shadow-sm hover:shadow-md mb-6" 
                    rows={4} 
                  />
                  
                  <div className="flex justify-between">
                    <Button variant="outline" onClick={() => setCurrentStep(3)}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid(4) || isSubmitting}
                      className="bg-gradient-to-r from-emerald-800 to-emerald-900 hover:from-emerald-900 hover:to-emerald-950 text-white font-semibold shadow-lg rounded-xl px-6 py-3 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Proceed to Payment
                          <ArrowRight className="ml-2 w-5 h-5 text-white" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Professional Carpet Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          const serviceType = 'carpet-cleaning';
          return ServiceTypeStandardizer.standardizePaymentServiceType({
            ...formData,
            serviceType: serviceType,
            cleaningType: 'carpet',
            frequency: 'one-time',
            totalPrice: calculateTotalPrice()
          });
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </AnimatedBackground>
  );
};

export default ModernCarpetCleaningForm; 