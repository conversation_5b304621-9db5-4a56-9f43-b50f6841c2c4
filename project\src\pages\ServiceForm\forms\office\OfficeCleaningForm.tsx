import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { ProgressBar } from '../../../../components/forms/ProgressBar';
import { ServiceTypeSelector } from './components/ServiceTypeSelector';
import { PropertyDetails } from './components/PropertyDetails';
import { CleaningDetails } from './components/CleaningDetails';
import { ServiceSelection } from './components/ServiceSelection';
import { ServiceScheduling } from './components/ServiceScheduling';
import { ContactInfo } from './components/ContactInfo';
import { SlideTransition } from '../../../../components/animations/SlideTransition';
// import { useFormValidation } from '../../../../lib/hooks/useFormValidation';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { submitBookingForm } from '../../../../lib/api/bookingForms';
import { steps, initialFormData } from './types';
import type { FormData } from './types';

interface OfficeCleaningFormProps {
  onBack: () => void;
}

export function OfficeCleaningForm({ onBack }: OfficeCleaningFormProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, saveFormData } = useAuth();
  
  // Check for pre-selected service and skip first step if present
  const hasPreSelection = location.state?.preSelectedService === 'office';
  const initialStep = hasPreSelection ? 1 : 0;
  
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [formData, setFormData] = useState<FormData>({
    ...initialFormData,
    // Pre-populate service type if pre-selected
    serviceType: hasPreSelection ? 'office' : initialFormData.serviceType
  });
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Simplified step validation (avoids undefined serviceType error)
  const validateStep = (step: number, data: FormData): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    switch (step) {
      case 0:
        if (!data.serviceType) errors.push('Please select a service type');
        break;
      case 1:
        if (!data.propertyDetails.propertyType) errors.push('Property type is required');
        if (data.propertyDetails.squareFootage <= 0) errors.push('Square footage must be greater than 0');
        break;
      case 2:
        if (!data.cleaningDetails.frequency) errors.push('Cleaning frequency is required');
        break;
      case 3:
        // No required fields on this step (services)
        break;
      case 4:
        if (!data.schedule.date) errors.push('Please select a preferred date');
        if (!data.schedule.timeSlot) errors.push('Please select a time slot');
        break;
      case 5:
        if (!data.contact.fullName.trim()) errors.push('Full name is required');
        if (!data.contact.email.trim()) errors.push('Email is required');
        if (!data.contact.phone.trim()) errors.push('Phone is required');
        break;
      default:
        break;
    }

    return { isValid: errors.length === 0, errors };
  };


  const handleNext = async () => {
    const validation = validateStep(currentStep, formData);
    
    if (!validation.isValid) {
      setValidationError(validation.errors.join('\n'));
      return;
    }

    setValidationError(null);

    if (currentStep === steps.length - 1) {
      await handleSubmit();
    } else {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    }
  };

  const handlePrev = () => {
    // If we're at step 0, or at step 1 with pre-selection, go back to previous page
    if (currentStep === 0 || (currentStep === 1 && hasPreSelection)) {
      onBack();
    } else {
      setCurrentStep((prev) => Math.max(prev - 1, 0));
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setValidationError(null);

      if (!user) {
        // Save form data and redirect to login/signup
        saveFormData(formData);
        navigate('/auth/login', { 
          state: { from: location.pathname }
        });
        return;
      }

      await submitBookingForm(formData, 'office', user);
      navigate('/thank-you', { state: { formData } });
    } catch (err) {
      console.error('Error submitting form:', err);
      setValidationError(
        err instanceof Error ? err.message : 'Failed to submit request'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ServiceTypeSelector
            selected={formData.serviceType}
            onChange={(type) => setFormData({ ...formData, serviceType: type })}
          />
        );
      case 1:
        return (
          <PropertyDetails
            details={formData.propertyDetails}
            onChange={(details) => setFormData({ ...formData, propertyDetails: details })}
          />
        );
      case 2:
        return (
          <CleaningDetails
            details={formData.cleaningDetails}
            onChange={(details) => setFormData({ ...formData, cleaningDetails: details })}
          />
        );
      case 3:
        return (
          <ServiceSelection
            services={formData.services}
            onChange={(services) => setFormData({ ...formData, services: services })}
          />
        );
      case 4:
        return (
          <ServiceScheduling
            schedule={formData.schedule}
            onChange={(schedule) => setFormData({ ...formData, schedule: schedule })}
          />
        );
      case 5:
        return (
          <ContactInfo
            contact={formData.contact}
            onChange={(contact) => setFormData({ ...formData, contact: contact })}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-[400px] max-w-4xl mx-auto bg-white rounded-2xl shadow-xl p-8">
      {/* Progress Bar */}
      <ProgressBar steps={steps} currentStep={currentStep} color="#93C572" />

      {/* Validation Error */}
      {validationError && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" />
            <div className="text-red-600 whitespace-pre-line">
              {validationError}
            </div>
          </div>
        </div>
      )}

      {/* Form Content */}
      <div className="mt-12 mb-12">
        <SlideTransition>
          <div className="transition-all duration-300 ease-in-out">
            {renderStep()}
          </div>
        </SlideTransition>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-between pt-8 border-t border-gray-200">
        <Button
          variant="outline"
          onClick={handlePrev}
          disabled={isSubmitting}
          size="lg"
          className="min-w-[120px]"
        >
          {currentStep === 0 ? 'Cancel' : 'Back'}
        </Button>
        
        <Button
          onClick={handleNext}
          disabled={isSubmitting}
          size="lg"
          className="min-w-[120px]"
        >
          {currentStep === steps.length - 1 ? 'Submit Request' : 'Continue'}
        </Button>
      </div>
    </div>
  );
}
