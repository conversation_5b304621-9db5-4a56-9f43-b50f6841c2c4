import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  Building, Briefcase, Heart, Users, Phone, Mail, User,
  ArrowRight, Home, CheckCircle, Clock, X, AlertCircle
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';

interface OfficeFormData {
  servicePackage: string;
  propertyType: string;
  industryType: string;
  squareFootage: number;
  floors: number;
  workstations: number;
  conferenceRooms: number;
  restrooms: number;
  breakRooms: number;
  propertyAddress: string;
  accessHours: string;
  securityRequirements: string;
  parkingAvailable: boolean;
  serviceFrequency: string;
  preferredTime: string;
  priorityAreas: string[];
  additionalServices: string[];
  specialInstructions: string;
  selectedServices: string[];
  startDate: string;
  wantsRecurringContract: boolean;
  contractLength?: string;
  budgetRange: string;
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  companyName: string;
  jobTitle: string;
}

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  companyName?: string;
  propertyAddress?: string;
  propertyType?: string;
  industryType?: string;
  squareFootage?: string;
  serviceFrequency?: string;
  preferredTime?: string;
}

const steps = [
  { id: 1, name: 'Service Type' },
  { id: 2, name: 'Property Details' },
  { id: 3, name: 'Cleaning Scope' },
  { id: 4, name: 'Services' },
  { id: 5, name: 'Schedule' },
  { id: 6, name: 'Contact' },
];

const servicePackages = [
  { id: 'standard', name: 'Standard Office Cleaning', description: 'Regular maintenance cleaning for office spaces', icon: <Home /> },
  { id: 'executive', name: 'Executive Office Package', description: 'Premium cleaning for high-end office spaces', icon: <Briefcase /> },
  { id: 'medical', name: 'Medical Office Cleaning', description: 'Specialized cleaning for medical facilities', icon: <Heart /> },
  { id: 'corporate', name: 'Corporate Package', description: 'Full-service cleaning for large corporate offices', icon: <Building /> },
  { id: 'small-business', name: 'Small Business Package', description: 'Affordable cleaning for small offices', icon: <Users /> },
];

const serviceFrequencies = [
  { id: 'daily', name: 'Daily Service' },
  { id: 'weekly', name: 'Weekly Service' },
  { id: 'bi-weekly', name: 'Bi-Weekly Service' },
  { id: 'monthly', name: 'Monthly Service' },
];

const preferredTimes = [
  { id: 'early-morning', name: 'Early Morning (6AM - 9AM)' },
  { id: 'morning', name: 'Morning (9AM - 12PM)' },
  { id: 'afternoon', name: 'Afternoon (12PM - 5PM)' },
  { id: 'evening', name: 'Evening (5PM - 9PM)' },
];

const priorityAreas = [
  { id: 'reception', name: 'Reception Area' },
  { id: 'executive', name: 'Executive Offices' },
  { id: 'conference', name: 'Conference Rooms' },
  { id: 'break', name: 'Break Rooms' },
  { id: 'server', name: 'Server Rooms' },
  { id: 'private', name: 'Private Offices' },
  { id: 'workspace', name: 'Open Workspace' },
  { id: 'copy', name: 'Copy/Print Rooms' },
];

const propertyTypes = [
  { id: 'office-building', name: 'Office Building' },
  { id: 'medical-facility', name: 'Medical Facility' },
  { id: 'retail-space', name: 'Retail Space' },
  { id: 'warehouse', name: 'Warehouse/Distribution' },
  { id: 'government', name: 'Government Building' },
  { id: 'educational', name: 'Educational Facility' },
  { id: 'mixed-use', name: 'Mixed-Use Building' },
];

const industryTypes = [
  { id: 'technology', name: 'Technology' },
  { id: 'healthcare', name: 'Healthcare' },
  { id: 'finance', name: 'Finance/Banking' },
  { id: 'legal', name: 'Legal Services' },
  { id: 'consulting', name: 'Consulting' },
  { id: 'real-estate', name: 'Real Estate' },
  { id: 'insurance', name: 'Insurance' },
  { id: 'manufacturing', name: 'Manufacturing' },
  { id: 'retail', name: 'Retail' },
  { id: 'government', name: 'Government' },
  { id: 'education', name: 'Education' },
  { id: 'non-profit', name: 'Non-Profit' },
  { id: 'other', name: 'Other' },
];

const additionalServices = [
  { id: 'restocking', name: 'Supply Restocking Service' },
  { id: 'waste', name: 'Waste Management & Recycling' },
  { id: 'carpet-cleaning', name: 'Deep Carpet Cleaning' },
  { id: 'window-cleaning', name: 'Interior Window Cleaning' },
  { id: 'floor-waxing', name: 'Floor Waxing & Polishing' },
  { id: 'sanitization', name: 'Deep Sanitization' },
  { id: 'post-event', name: 'Post-Event Cleanup' },
  { id: 'move-in-out', name: 'Move-In/Move-Out Cleaning' },
];

// Modern custom select component
const ModernSelect = ({ label, value, onChange, options, placeholder }: { 
  label: string, 
  value: string, 
  onChange: (value: string) => void, 
  options: { id: string, name: string }[], 
  placeholder: string 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="space-y-2">
      <label className="text-sm font-semibold text-white block">{label}</label>
      <div className="relative">
        <motion.button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-left text-white focus:outline-none focus:border-green-400 focus:bg-white/15 transition-all duration-300 flex items-center justify-between"
          whileTap={{ scale: 0.98 }}
        >
          <span className={value ? 'text-white' : 'text-gray-400'}>
            {value ? options.find(opt => opt.id === value)?.name : placeholder}
          </span>
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ArrowRight className="w-5 h-5 rotate-90" />
          </motion.div>
        </motion.button>
        
        <AnimatePresence>
          {isOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 mt-2 bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl overflow-hidden z-50 shadow-2xl"
            >
              {options.map((option) => (
                <motion.button
                  key={option.id}
                  type="button"
                  onClick={() => {
                    onChange(option.id);
                    setIsOpen(false);
                  }}
                  className="w-full p-4 text-left text-white hover:bg-white/10 transition-colors duration-200 border-b border-white/10 last:border-b-0"
                  whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}
                >
                  {option.name}
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};



// Enhanced Email Validation
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Enhanced Phone Number Formatting
const formatPhoneNumber = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  } else if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Phone Number Validation
const validatePhoneNumber = (phone: string): boolean => {
  const cleaned = phone.replace(/\D/g, '');
  return cleaned.length === 10 || (cleaned.length === 11 && cleaned[0] === '1');
};

// Address Validation (basic)
const validateAddress = (address: string): boolean => {
  return address.trim().length >= 10 && /\d/.test(address) && /[a-zA-Z]/.test(address);
};

const BrandAlignedFloorCareForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<Partial<OfficeFormData>>({
    servicePackage: 'standard',
    priorityAreas: [],
    additionalServices: [],
    parkingAvailable: false,
    squareFootage: 1000,
    floors: 1,
    workstations: 10,
    conferenceRooms: 1,
    restrooms: 2,
    breakRooms: 1,
    wantsRecurringContract: false,
  });

  // Save form data to localStorage on changes
  useEffect(() => {
    localStorage.setItem('officeFormData', JSON.stringify(formData));
  }, [formData]);

  // Load form data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('officeFormData');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        setFormData(prev => ({ ...prev, ...parsedData }));
      } catch (error) {
        console.error('Error loading saved form data:', error);
      }
    }
  }, []);

  // Enhanced form field validation
  const validateField = (fieldName: keyof FormErrors, value: string): string | undefined => {
    switch (fieldName) {
      case 'email':
        if (!value) return 'Email is required';
        if (!validateEmail(value)) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value) return 'Phone number is required';
        if (!validatePhoneNumber(value)) return 'Please enter a valid phone number';
        break;
      case 'firstName':
        if (!value || value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value || value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'companyName':
        if (!value || value.trim().length < 2) return 'Company name is required';
        break;
      case 'propertyAddress':
        if (!value) return 'Property address is required';
        if (!validateAddress(value)) return 'Please enter a complete address with street number and name';
        break;
      case 'propertyType':
        if (!value) return 'Please select a property type';
        break;
      case 'industryType':
        if (!value) return 'Please select an industry type';
        break;
      case 'squareFootage':
        if (!value || parseInt(value) < 100) return 'Square footage must be at least 100 sq ft';
        break;
      case 'serviceFrequency':
        if (!value) return 'Please select a service frequency';
        break;
      case 'preferredTime':
        if (!value) return 'Please select a preferred time';
        break;
    }
    return undefined;
  };

  // Enhanced handleNext with validation
  const handleNext = () => {
    if (currentStep < steps.length) {
      setIsLoading(true);
      
      // Validate current step
      const errors: FormErrors = {};
      
      if (currentStep === 2) {
        errors.propertyType = validateField('propertyType', formData.propertyType || '');
        errors.industryType = validateField('industryType', formData.industryType || '');
        errors.squareFootage = validateField('squareFootage', formData.squareFootage?.toString() || '');
        errors.propertyAddress = validateField('propertyAddress', formData.propertyAddress || '');
      } else if (currentStep === 3) {
        errors.serviceFrequency = validateField('serviceFrequency', formData.serviceFrequency || '');
        errors.preferredTime = validateField('preferredTime', formData.preferredTime || '');
      } else if (currentStep === 6) {
        errors.firstName = validateField('firstName', formData.firstName || '');
        errors.lastName = validateField('lastName', formData.lastName || '');
        errors.email = validateField('email', formData.email || '');
        errors.phone = validateField('phone', formData.phone || '');
        errors.companyName = validateField('companyName', formData.companyName || '');
      }
      
      // Filter out undefined errors
      const filteredErrors = Object.fromEntries(
        Object.entries(errors).filter((entry) => entry[1] !== undefined)
      );
      
      setFormErrors(filteredErrors);
      
      if (Object.keys(filteredErrors).length === 0) {
        setTimeout(() => {
          setCurrentStep(currentStep + 1);
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
      }
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const isStepValid = () => {
    switch (currentStep) {
      case 1:
        return formData.servicePackage;
      case 2:
        return formData.propertyType && 
               formData.industryType && 
               formData.squareFootage && 
               formData.propertyAddress &&
               !formErrors.propertyType &&
               !formErrors.industryType &&
               !formErrors.squareFootage &&
               !formErrors.propertyAddress;
      case 3:
        return formData.serviceFrequency && 
               formData.preferredTime &&
               !formErrors.serviceFrequency &&
               !formErrors.preferredTime;
      case 6:
        return formData.firstName && 
               formData.lastName && 
               formData.email && 
               formData.phone && 
               formData.companyName &&
               !formErrors.firstName &&
               !formErrors.lastName &&
               !formErrors.email &&
               !formErrors.phone &&
               !formErrors.companyName;
      default:
        return true;
    }
  };

  // Enhanced input handlers with validation
  const handleEmailChange = (value: string) => {
    setFormData({...formData, email: value});
    const error = validateField('email', value);
    setFormErrors(prev => ({ ...prev, email: error }));
  };

  const handlePhoneChange = (value: string) => {
    const formatted = formatPhoneNumber(value);
    setFormData({...formData, phone: formatted});
    const error = validateField('phone', value);
    setFormErrors(prev => ({ ...prev, phone: error }));
  };

  const handleAddressChange = (value: string) => {
    setFormData({...formData, propertyAddress: value});
    const error = validateField('propertyAddress', value);
    setFormErrors(prev => ({ ...prev, propertyAddress: error }));
  };

  const handleNameChange = (field: 'firstName' | 'lastName', value: string) => {
    setFormData({...formData, [field]: value});
    const error = validateField(field, value);
    setFormErrors(prev => ({ ...prev, [field]: error }));
  };

  const handleCompanyNameChange = (value: string) => {
    setFormData({...formData, companyName: value});
    const error = validateField('companyName', value);
    setFormErrors(prev => ({ ...prev, companyName: error }));
  };

  const handlePriorityAreaToggle = (areaId: string) => {
    const currentAreas = formData.priorityAreas || [];
    if (currentAreas.includes(areaId)) {
      setFormData({
        ...formData,
        priorityAreas: currentAreas.filter(id => id !== areaId)
      });
    } else {
      setFormData({
        ...formData,
        priorityAreas: [...currentAreas, areaId]
      });
    }
  };

  const handleAdditionalServiceToggle = (serviceId: string) => {
    const currentServices = formData.additionalServices || [];
    if (currentServices.includes(serviceId)) {
      setFormData({
        ...formData,
        additionalServices: currentServices.filter(id => id !== serviceId)
      });
    } else {
      setFormData({
        ...formData,
        additionalServices: [...currentServices, serviceId]
      });
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate all contact fields before submission
      const contactErrors: FormErrors = {};
      contactErrors.firstName = validateField('firstName', formData.firstName || '');
      contactErrors.lastName = validateField('lastName', formData.lastName || '');
      contactErrors.email = validateField('email', formData.email || '');
      contactErrors.phone = validateField('phone', formData.phone || '');
      contactErrors.companyName = validateField('companyName', formData.companyName || '');
      
      const filteredContactErrors = Object.fromEntries(
        Object.entries(contactErrors).filter((entry) => entry[1] !== undefined)
      );
      
      if (Object.keys(filteredContactErrors).length > 0) {
        setFormErrors(filteredContactErrors);
        setIsLoading(false);
        return;
      }

      // Submit form data to Supabase database
      const { supabase } = await import('../../../../lib/supabase/client');
      
      const bookingData = {
        user_id: user?.id || null,
        service_type: 'office',
        property_details: {
          propertyType: formData.propertyType,
          industryType: formData.industryType,
          address: formData.propertyAddress,
          squareFootage: formData.squareFootage,
          floors: formData.floors,
          workstations: formData.workstations,
          conferenceRooms: formData.conferenceRooms,
          restrooms: formData.restrooms,
          breakRooms: formData.breakRooms,
          accessHours: formData.accessHours,
          securityRequirements: formData.securityRequirements,
          parkingAvailable: formData.parkingAvailable,
        },
        service_details: {
          servicePackage: formData.servicePackage,
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          priorityAreas: formData.priorityAreas,
          additionalServices: formData.additionalServices,
          selectedServices: formData.selectedServices,
          wantsRecurringContract: formData.wantsRecurringContract,
          contractLength: formData.contractLength,
          budgetRange: formData.budgetRange,
        },
        schedule: {
          serviceFrequency: formData.serviceFrequency,
          preferredTime: formData.preferredTime,
          startDate: formData.startDate,
        },
        contact: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          companyName: formData.companyName,
          jobTitle: formData.jobTitle,
        },
        status: 'pending',
        special_instructions: formData.specialInstructions,
        metadata: {
          submittedAt: new Date().toISOString(),
          requestType: 'estimate',
        }
      };

      const { data, error } = await supabase!
        .from('booking_forms')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        console.error('Error saving booking:', error);
        throw new Error('Failed to save booking');
      }

      console.log('Office booking saved successfully:', data);
      
      // Clear form data from localStorage after successful submission
      localStorage.removeItem('officeFormData');
      
      // Show success popup instead of navigating immediately
      setShowSuccessPopup(true);
      
    } catch (error) {
      console.error('Error scheduling estimate:', error);
      // Handle error - maybe show a toast notification
    } finally {
      setIsLoading(false);
    }
  };

  const handleClosePopup = () => {
    setShowSuccessPopup(false);
    // If user is authenticated, take them to dashboard; otherwise to login page
    if (user) {
      navigate('/accountdashboard');
    } else {
      // Store the intent to redirect to dashboard after login
      localStorage.setItem('redirectAfterLogin', '/accountdashboard');
      navigate('/auth/login');
    }
  };

  return (
    <AnimatedBackground>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
        <div className="w-full max-w-4xl mx-auto">
          {/* Modern Progress Bar */}
          <div className="mb-12">
            {/* Step Counter */}
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-2 border border-white/20">
                <span className="text-white font-medium">
                  Step {currentStep} of {steps.length}
                </span>
              </div>
            </div>
            
            {/* Progress Line */}
            <div className="relative">
              <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
                  initial={{ width: '16.67%' }}
                  animate={{ width: `${(currentStep / steps.length) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              
              {/* Step Dots */}
              <div className="absolute inset-0 flex items-center justify-between px-1">
                {steps.map((step) => (
                  <motion.div
                    key={step.id}
                    className={`w-4 h-4 rounded-full border-2 ${
                      currentStep >= step.id 
                        ? 'bg-green-400 border-green-400' 
                        : 'bg-white/10 border-white/30'
                    }`}
                    animate={{
                      scale: currentStep === step.id ? 1.2 : 1,
                    }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </div>
            
            {/* Current Step Label */}
            <div className="text-center mt-4">
              <h1 className="text-xl font-semibold text-white">
                {steps[currentStep - 1]?.name}
              </h1>
            </div>
          </div>

          {/* Form Content */}
          <motion.div 
            className="rounded-3xl shadow-2xl p-8 md:p-12"
            style={{
              background: 'linear-gradient(135deg, rgba(15, 32, 39, 0.95) 0%, rgba(32, 58, 67, 0.95) 50%, rgba(44, 85, 48, 0.95) 100%)',
              border: '1px solid rgba(34, 197, 94, 0.3)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
              backdropFilter: 'blur(12px)',
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <AnimatePresence mode="wait">
              {currentStep === 1 && (
                <motion.div
                  key="step1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <div className="text-center mb-8">
                    <h2 className="text-3xl font-bold text-white mb-3">Choose Your Service Package</h2>
                    <p className="text-gray-300 text-lg">Select the cleaning package that best fits your office needs</p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {servicePackages.map((pkg) => (
                      <motion.div
                        key={pkg.id}
                        whileHover={{ scale: 1.02, y: -4 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setFormData({ ...formData, servicePackage: pkg.id })}
                        className={`group relative p-8 rounded-2xl cursor-pointer transition-all duration-300 ${
                          formData.servicePackage === pkg.id 
                            ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                            : 'bg-white/5 border-2 border-white/10 hover:border-white/30 hover:bg-white/10'
                        }`}
                      >
                        {/* Selection Indicator */}
                        {formData.servicePackage === pkg.id && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute top-4 right-4 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                          >
                            <div className="w-3 h-3 bg-white rounded-full" />
                          </motion.div>
                        )}
                        
                        <div className="flex items-start gap-4">
                          <div className={`p-3 rounded-xl ${
                            formData.servicePackage === pkg.id ? 'bg-green-400/20 text-green-300' : 'bg-white/10 text-gray-300 group-hover:text-white'
                          } transition-colors duration-300`}>
                            {pkg.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="font-bold text-white text-lg mb-2">{pkg.name}</h3>
                            <p className="text-gray-300 text-sm leading-relaxed">{pkg.description}</p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={() => navigate('/')}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 2 && (
                <motion.div
                  key="step2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Property Details</h2>
                  <p className="text-gray-300 mb-6">Tell us about your office space.</p>
                  
                  <div className="space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <ModernSelect
                        label="Property Type"
                        value={formData.propertyType || ''}
                        onChange={(value) => setFormData({...formData, propertyType: value})}
                        options={propertyTypes}
                        placeholder="Select property type"
                      />
                      <ModernSelect
                        label="Industry Type"
                        value={formData.industryType || ''}
                        onChange={(value) => setFormData({...formData, industryType: value})}
                        options={industryTypes}
                        placeholder="Select industry type"
                      />
                    </div>

                    {/* Space Details */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Space Details</h3>
                       <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                          {/* Square Footage - Typeable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Square Footage</label>
                            <div className="relative">
                              <input 
                                type="number" 
                                placeholder="e.g. 2500"
                                value={formData.squareFootage || ''}
                                onChange={(e) => setFormData({...formData, squareFootage: parseInt(e.target.value) || 0})}
                                className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                              />
                              <span className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 text-sm">sq ft</span>
                            </div>
                          </div>
                          
                          {/* Floors - Editable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Floors</label>
                            <input 
                              type="number" 
                              placeholder="e.g. 3"
                              min="1"
                              value={formData.floors || ''}
                              onChange={(e) => setFormData({...formData, floors: parseInt(e.target.value) || 1})}
                              className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            />
                          </div>
                          
                          {/* Workstations - Editable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Workstations</label>
                            <input 
                              type="number" 
                              placeholder="e.g. 25"
                              min="0"
                              value={formData.workstations || ''}
                              onChange={(e) => setFormData({...formData, workstations: parseInt(e.target.value) || 0})}
                              className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            />
                          </div>
                          
                          {/* Conference Rooms - Editable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Conference Rooms</label>
                            <input 
                              type="number" 
                              placeholder="e.g. 4"
                              min="0"
                              value={formData.conferenceRooms || ''}
                              onChange={(e) => setFormData({...formData, conferenceRooms: parseInt(e.target.value) || 0})}
                              className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            />
                          </div>
                          
                          {/* Restrooms - Editable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Restrooms</label>
                            <input 
                              type="number" 
                              placeholder="e.g. 6"
                              min="0"
                              value={formData.restrooms || ''}
                              onChange={(e) => setFormData({...formData, restrooms: parseInt(e.target.value) || 0})}
                              className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            />
                          </div>
                          
                          {/* Break Rooms - Editable Input */}
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">Break Rooms</label>
                            <input 
                              type="number" 
                              placeholder="e.g. 2"
                              min="0"
                              value={formData.breakRooms || ''}
                              onChange={(e) => setFormData({...formData, breakRooms: parseInt(e.target.value) || 0})}
                              className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg font-semibold placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            />
                          </div>
                       </div>
                    </div>
                    
                    {/* Location & Access */}
                     <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Location & Access</h3>
                       <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-semibold text-white block">
                              Property Address <span className="text-red-400">*</span>
                            </label>
                            <input 
                              type="text" 
                              placeholder="123 Main St, City, State 12345" 
                              className={`w-full p-4 rounded-xl border bg-slate-900 text-white placeholder-gray-400 focus:outline-none hover:bg-slate-800 transition-colors duration-300 shadow-lg ${
                                formErrors.propertyAddress 
                                  ? 'border-red-400 focus:border-red-400' 
                                  : 'border-slate-700 focus:border-green-400 hover:border-slate-600'
                              }`}
                              value={formData.propertyAddress || ''}
                              onChange={(e) => handleAddressChange(e.target.value)}
                            />
                            {formErrors.propertyAddress && (
                              <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="flex items-center gap-2 text-red-400 text-sm"
                              >
                                <AlertCircle className="w-4 h-4" />
                                {formErrors.propertyAddress}
                              </motion.div>
                            )}
                          </div>
                          <input 
                            type="text" 
                            placeholder="Access Hours (e.g., After 6 PM weekdays)" 
                            className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg"
                            value={formData.accessHours || ''}
                            onChange={(e) => setFormData({...formData, accessHours: e.target.value})}
                          />
                          <textarea 
                            placeholder="Security Requirements (e.g., Desk check-in)" 
                            className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 resize-none shadow-lg" 
                            rows={3}
                            value={formData.securityRequirements || ''}
                            onChange={(e) => setFormData({...formData, securityRequirements: e.target.value})}
                          />
                          <label className="flex items-center gap-3 text-white cursor-pointer">
                            <input 
                              type="checkbox" 
                              className="w-4 h-4 text-green-400 bg-gray-700 border-gray-600 rounded focus:ring-green-400" 
                              checked={formData.parkingAvailable || false}
                              onChange={(e) => setFormData({...formData, parkingAvailable: e.target.checked})}
                            />
                            <span>Parking available for cleaning staff</span>
                          </label>
                       </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={handleBack}
                      disabled={isLoading}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Back
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Cleaning Scope</h2>
                  <p className="text-gray-300 mb-6">Customize your cleaning schedule and services.</p>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Service Frequency</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {serviceFrequencies.map(freq => (
                          <button 
                            key={freq.id} 
                            onClick={() => setFormData({...formData, serviceFrequency: freq.id})}
                            className={`p-4 border rounded-lg text-white transition-all duration-200 ${
                              formData.serviceFrequency === freq.id 
                                ? 'bg-green-400/20 border-green-400 text-green-300' 
                                : 'border-gray-600 hover:border-gray-400 hover:bg-white/5'
                            }`}
                          >
                            {freq.name}
                          </button>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Preferred Time</h3>
                       <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {preferredTimes.map(time => (
                          <button 
                            key={time.id} 
                            onClick={() => setFormData({...formData, preferredTime: time.id})}
                            className={`p-4 border rounded-lg text-white transition-all duration-200 ${
                              formData.preferredTime === time.id 
                                ? 'bg-green-400/20 border-green-400 text-green-300' 
                                : 'border-gray-600 hover:border-gray-400 hover:bg-white/5'
                            }`}
                          >
                            {time.name}
                          </button>
                        ))}
                      </div>
                    </div>
                     <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Priority Areas</h3>
                       <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {priorityAreas.map(area => (
                          <button 
                            key={area.id} 
                            onClick={() => handlePriorityAreaToggle(area.id)}
                            className={`p-4 border rounded-lg text-white transition-all duration-200 ${
                              formData.priorityAreas?.includes(area.id) 
                                ? 'bg-green-400/20 border-green-400 text-green-300' 
                                : 'border-gray-600 hover:border-gray-400 hover:bg-white/5'
                            }`}
                          >
                            {area.name}
                          </button>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Additional Services</h3>
                       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {additionalServices.map(service => (
                          <button 
                            key={service.id} 
                            onClick={() => handleAdditionalServiceToggle(service.id)}
                            className={`p-4 border rounded-lg text-white transition-all duration-200 ${
                              formData.additionalServices?.includes(service.id) 
                                ? 'bg-green-400/20 border-green-400 text-green-300' 
                                : 'border-gray-600 hover:border-gray-400 hover:bg-white/5'
                            }`}
                          >
                            {service.name}
                          </button>
                        ))}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Special Instructions</h3>
                      <textarea placeholder="Additional Notes" className="w-full bg-white/10 p-3 rounded-lg border border-white/20" rows={3}></textarea>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button onClick={handleNext}>Continue <ArrowRight className="ml-2 w-4 h-4" /></Button>
                  </div>
                </motion.div>
              )}
              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Services & Equipment</h2>
                  <p className="text-gray-300 mb-6">Select additional services and equipment needs.</p>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Equipment & Supplies</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <label className="flex items-center gap-3 p-4 border border-gray-600 rounded-lg hover:border-gray-400 cursor-pointer">
                          <input type="checkbox" className="w-4 h-4 text-green-400 bg-gray-700 border-gray-600 rounded" />
                          <span className="text-white">Eco-friendly cleaning products</span>
                        </label>
                        <label className="flex items-center gap-3 p-4 border border-gray-600 rounded-lg hover:border-gray-400 cursor-pointer">
                          <input type="checkbox" className="w-4 h-4 text-green-400 bg-gray-700 border-gray-600 rounded" />
                          <span className="text-white">HEPA vacuum systems</span>
                        </label>
                        <label className="flex items-center gap-3 p-4 border border-gray-600 rounded-lg hover:border-gray-400 cursor-pointer">
                          <input type="checkbox" className="w-4 h-4 text-green-400 bg-gray-700 border-gray-600 rounded" />
                          <span className="text-white">Microfiber cleaning cloths</span>
                        </label>
                        <label className="flex items-center gap-3 p-4 border border-gray-600 rounded-lg hover:border-gray-400 cursor-pointer">
                          <input type="checkbox" className="w-4 h-4 text-green-400 bg-gray-700 border-gray-600 rounded" />
                          <span className="text-white">Disinfection equipment</span>
                        </label>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-3">Special Requirements</h3>
                      <textarea 
                        placeholder="Any special requirements, allergies, or preferences..."
                        className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400" 
                        rows={4}
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button onClick={handleNext}>Continue <ArrowRight className="ml-2 w-4 h-4" /></Button>
                  </div>
                </motion.div>
              )}
              
              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Schedule & Preferences</h2>
                  <p className="text-gray-300 mb-6">Set your cleaning schedule and preferences.</p>
                  
                  <div className="space-y-8">
                    {/* Preferred Start Date */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Preferred Start Date</h3>
                      <div className="relative max-w-md">
                        <input 
                          type="date" 
                          className="w-full p-4 rounded-xl border border-slate-700 bg-slate-900 text-white text-lg focus:outline-none focus:border-green-400 hover:bg-slate-800 hover:border-slate-600 transition-colors duration-300 shadow-lg [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert"
                          min={new Date().toISOString().split('T')[0]}
                          value={formData.startDate || ''}
                          onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                        />
                      </div>
                    </div>
                    
                    {/* Recurring Contract */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Do you want a recurring contract?</h3>
                      <div className="grid grid-cols-2 gap-4 max-w-md">
                        {[
                          { id: 'yes', label: 'Yes', value: true, desc: 'Ongoing service with contract' },
                          { id: 'no', label: 'No', value: false, desc: 'One-time or as-needed service' }
                        ].map(option => (
                          <motion.button 
                            key={option.id}
                            onClick={() => setFormData({...formData, wantsRecurringContract: option.value, contractLength: !option.value ? undefined : formData.contractLength})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.wantsRecurringContract === option.value 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-semibold text-lg">{option.label}</span>
                              {formData.wantsRecurringContract === option.value && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{option.desc}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Contract Length - Only show if recurring selected */}
                    {formData.wantsRecurringContract && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <h3 className="text-lg font-semibold text-white mb-4">Contract Length</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {[
                            { id: '3 months', label: '3 Months', desc: 'Short-term commitment' },
                            { id: '6 months', label: '6 Months', desc: 'Popular choice' },
                            { id: '12 months', label: '12 Months', desc: 'Best value & savings' }
                          ].map(contract => (
                            <motion.button 
                              key={contract.id}
                              onClick={() => setFormData({...formData, contractLength: contract.id})}
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                                formData.contractLength === contract.id 
                                  ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                  : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-white font-semibold text-lg">{contract.label}</span>
                                {formData.contractLength === contract.id && (
                                  <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                  >
                                    <div className="w-3 h-3 bg-white rounded-full" />
                                  </motion.div>
                                )}
                              </div>
                              <p className="text-gray-300 text-sm">{contract.desc}</p>
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                    )}
                    
                    {/* Budget Range */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Budget Range (Monthly)</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {[
                          { id: '$500-1000', label: '$500 - $1,000', desc: 'Small office spaces' },
                          { id: '$1000-2000', label: '$1,000 - $2,000', desc: 'Medium office spaces' },
                          { id: '$2000-5000', label: '$2,000 - $5,000', desc: 'Large office spaces' },
                          { id: '$5000+', label: '$5,000+', desc: 'Enterprise facilities' }
                        ].map(budget => (
                          <motion.button 
                            key={budget.id}
                            onClick={() => setFormData({...formData, budgetRange: budget.id})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.budgetRange === budget.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-semibold text-lg">{budget.label}</span>
                              {formData.budgetRange === budget.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{budget.desc}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={handleBack}
                      disabled={isLoading}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Back
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
              
              {currentStep === 3 && (
                <motion.div
                  key="step3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Cleaning Scope</h2>
                  <p className="text-gray-300 mb-6">Select your service frequency and preferred timing.</p>
                  
                  <div className="space-y-6">
                    {/* Service Frequency */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Service Frequency <span className="text-red-400">*</span></h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {serviceFrequencies.map((freq) => (
                          <motion.button 
                            key={freq.id}
                            onClick={() => setFormData({...formData, serviceFrequency: freq.id})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.serviceFrequency === freq.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white font-semibold text-lg">{freq.name}</span>
                              {formData.serviceFrequency === freq.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                      {formErrors.serviceFrequency && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4" />
                          {formErrors.serviceFrequency}
                        </motion.div>
                      )}
                    </div>

                    {/* Preferred Time */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Preferred Time <span className="text-red-400">*</span></h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {preferredTimes.map((time) => (
                          <motion.button 
                            key={time.id}
                            onClick={() => setFormData({...formData, preferredTime: time.id})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.preferredTime === time.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white font-semibold text-lg">{time.name}</span>
                              {formData.preferredTime === time.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                      {formErrors.preferredTime && (
                        <motion.div
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                        >
                          <AlertCircle className="w-4 h-4" />
                          {formErrors.preferredTime}
                        </motion.div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={handleBack}
                      disabled={isLoading}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Back
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 4 && (
                <motion.div
                  key="step4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Services & Priority Areas</h2>
                  <p className="text-gray-300 mb-6">Choose your priority areas and additional services.</p>
                  
                  <div className="space-y-8">
                    {/* Priority Areas */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Priority Areas</h3>
                      <p className="text-gray-300 text-sm mb-4">Select the areas that require special attention.</p>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {priorityAreas.map((area) => (
                          <motion.button 
                            key={area.id}
                            onClick={() => handlePriorityAreaToggle(area.id)}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-4 rounded-xl text-center transition-all duration-300 ${
                              formData.priorityAreas?.includes(area.id)
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <span className="text-white font-medium text-sm">{area.name}</span>
                            {formData.priorityAreas?.includes(area.id) && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-4 h-4 bg-green-400 rounded-full flex items-center justify-center mx-auto mt-2"
                              >
                                <div className="w-2 h-2 bg-white rounded-full" />
                              </motion.div>
                            )}
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    {/* Additional Services */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Additional Services</h3>
                      <p className="text-gray-300 text-sm mb-4">Select any additional services you'd like to include.</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {additionalServices.map((service) => (
                          <motion.button 
                            key={service.id}
                            onClick={() => handleAdditionalServiceToggle(service.id)}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-4 rounded-xl text-left transition-all duration-300 ${
                              formData.additionalServices?.includes(service.id)
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="text-white font-medium">{service.name}</span>
                              {formData.additionalServices?.includes(service.id) && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-2.5 h-2.5 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={handleBack}
                      disabled={isLoading}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Back
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}

              {currentStep === 5 && (
                <motion.div
                  key="step5"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Schedule & Budget</h2>
                  <p className="text-gray-300 mb-6">Set your start date, contract preferences, and budget range.</p>
                  
                  <div className="space-y-8">
                    {/* Start Date */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Preferred Start Date</h3>
                      <input 
                        type="date" 
                        className="w-full max-w-md bg-white/10 p-4 rounded-xl border border-white/20 text-white focus:outline-none focus:border-green-400 transition-colors duration-300"
                        value={formData.startDate || ''}
                        onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                        min={new Date().toISOString().split('T')[0]}
                      />
                    </div>
                    
                    {/* Special Instructions */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Special Instructions</h3>
                      <textarea 
                        placeholder="Any specific cleaning requirements, access instructions, or special considerations..."
                        className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-300 h-32 resize-none"
                        value={formData.specialInstructions || ''}
                        onChange={(e) => setFormData({...formData, specialInstructions: e.target.value})}
                      />
                    </div>
                    
                    {/* Access Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-4">Access Hours</h3>
                        <input 
                          type="text" 
                          placeholder="e.g., 6AM - 6PM weekdays"
                          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-300"
                          value={formData.accessHours || ''}
                          onChange={(e) => setFormData({...formData, accessHours: e.target.value})}
                        />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-4">Security Requirements</h3>
                        <input 
                          type="text" 
                          placeholder="e.g., Badge access required"
                          className="w-full bg-white/10 p-4 rounded-xl border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400 transition-colors duration-300"
                          value={formData.securityRequirements || ''}
                          onChange={(e) => setFormData({...formData, securityRequirements: e.target.value})}
                        />
                      </div>
                    </div>
                    
                    {/* Parking Available */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Parking Available?</h3>
                      <div className="grid grid-cols-2 gap-4 max-w-md">
                        {[
                          { id: 'yes', label: 'Yes', value: true, desc: 'On-site parking available' },
                          { id: 'no', label: 'No', value: false, desc: 'Street parking only' }
                        ].map(option => (
                          <motion.button 
                            key={option.id}
                            onClick={() => setFormData({...formData, parkingAvailable: option.value})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.parkingAvailable === option.value 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-semibold text-lg">{option.label}</span>
                              {formData.parkingAvailable === option.value && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{option.desc}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Recurring Contract */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Do you want a recurring contract?</h3>
                      <div className="grid grid-cols-2 gap-4 max-w-md">
                        {[
                          { id: 'yes', label: 'Yes', value: true, desc: 'Ongoing service with contract' },
                          { id: 'no', label: 'No', value: false, desc: 'One-time or as-needed service' }
                        ].map(option => (
                          <motion.button 
                            key={option.id}
                            onClick={() => setFormData({...formData, wantsRecurringContract: option.value, contractLength: !option.value ? undefined : formData.contractLength})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.wantsRecurringContract === option.value 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-semibold text-lg">{option.label}</span>
                              {formData.wantsRecurringContract === option.value && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{option.desc}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                    
                    {/* Contract Length - Only show if recurring selected */}
                    {formData.wantsRecurringContract && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        transition={{ duration: 0.3 }}
                      >
                        <h3 className="text-lg font-semibold text-white mb-4">Contract Length</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {[
                            { id: '3 months', label: '3 Months', desc: 'Short-term commitment' },
                            { id: '6 months', label: '6 Months', desc: 'Popular choice' },
                            { id: '12 months', label: '12 Months', desc: 'Best value & savings' }
                          ].map(contract => (
                            <motion.button 
                              key={contract.id}
                              onClick={() => setFormData({...formData, contractLength: contract.id})}
                              whileHover={{ scale: 1.02, y: -2 }}
                              whileTap={{ scale: 0.98 }}
                              className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                                formData.contractLength === contract.id 
                                  ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                  : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-white font-semibold text-lg">{contract.label}</span>
                                {formData.contractLength === contract.id && (
                                  <motion.div
                                    initial={{ scale: 0 }}
                                    animate={{ scale: 1 }}
                                    className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                  >
                                    <div className="w-3 h-3 bg-white rounded-full" />
                                  </motion.div>
                                )}
                              </div>
                              <p className="text-gray-300 text-sm">{contract.desc}</p>
                            </motion.button>
                          ))}
                        </div>
                      </motion.div>
                    )}
                    
                    {/* Budget Range */}
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-4">Budget Range (Monthly)</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {[
                          { id: '$500-1000', label: '$500 - $1,000', desc: 'Small office spaces' },
                          { id: '$1000-2000', label: '$1,000 - $2,000', desc: 'Medium office spaces' },
                          { id: '$2000-5000', label: '$2,000 - $5,000', desc: 'Large office spaces' },
                          { id: '$5000+', label: '$5,000+', desc: 'Enterprise facilities' }
                        ].map(budget => (
                          <motion.button 
                            key={budget.id}
                            onClick={() => setFormData({...formData, budgetRange: budget.id})}
                            whileHover={{ scale: 1.02, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                            className={`p-6 rounded-2xl text-left transition-all duration-300 ${
                              formData.budgetRange === budget.id 
                                ? 'bg-gradient-to-br from-green-500/20 to-emerald-600/20 border-2 border-green-400 shadow-lg shadow-green-400/20' 
                                : 'bg-slate-900 border-2 border-slate-700 hover:border-slate-600 hover:bg-slate-800 shadow-lg'
                            }`}
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-white font-semibold text-lg">{budget.label}</span>
                              {formData.budgetRange === budget.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-6 h-6 bg-green-400 rounded-full flex items-center justify-center"
                                >
                                  <div className="w-3 h-3 bg-white rounded-full" />
                                </motion.div>
                              )}
                            </div>
                            <p className="text-gray-300 text-sm">{budget.desc}</p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-12">
                    <Button 
                      variant="outline" 
                      onClick={handleBack}
                      disabled={isLoading}
                      className="px-6 py-3 rounded-xl border-white/20 text-white hover:bg-white/10"
                    >
                      Back
                    </Button>
                    <Button 
                      onClick={handleNext}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Continue <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
              
              {currentStep === 6 && (
                <motion.div
                  key="step6"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                >
                  <h2 className="text-2xl font-bold text-white mb-2">Contact Information</h2>
                  <p className="text-gray-300 mb-6">Let's get your contact details to finalize your quote.</p>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          First Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="text" 
                            placeholder="John"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.firstName 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-green-400'
                            }`}
                            value={formData.firstName || ''}
                            onChange={(e) => handleNameChange('firstName', e.target.value)}
                          />
                        </div>
                        {formErrors.firstName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.firstName}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Last Name <span className="text-red-400">*</span>
                        </label>
                        <input 
                          type="text" 
                          placeholder="Doe"
                          className={`w-full bg-white/10 p-3 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                            formErrors.lastName 
                              ? 'border-red-400 focus:border-red-400' 
                              : 'border-white/20 focus:border-green-400'
                          }`}
                          value={formData.lastName || ''}
                          onChange={(e) => handleNameChange('lastName', e.target.value)}
                        />
                        {formErrors.lastName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.lastName}
                          </motion.div>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Email Address <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="email" 
                            placeholder="<EMAIL>"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.email 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-green-400'
                            }`}
                            value={formData.email || ''}
                            onChange={(e) => handleEmailChange(e.target.value)}
                          />
                        </div>
                        {formErrors.email && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.email}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Phone Number <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="tel" 
                            placeholder="(*************"
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.phone 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-green-400'
                            }`}
                            value={formData.phone || ''}
                            onChange={(e) => handlePhoneChange(e.target.value)}
                          />
                        </div>
                        {formErrors.phone && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.phone}
                          </motion.div>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">
                          Company Name <span className="text-red-400">*</span>
                        </label>
                        <div className="relative">
                          <Building className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                          <input 
                            type="text" 
                            placeholder="Company Inc."
                            className={`w-full bg-white/10 p-3 pl-10 rounded-lg border text-white placeholder-gray-400 focus:outline-none transition-colors duration-300 ${
                              formErrors.companyName 
                                ? 'border-red-400 focus:border-red-400' 
                                : 'border-white/20 focus:border-green-400'
                            }`}
                            value={formData.companyName || ''}
                            onChange={(e) => handleCompanyNameChange(e.target.value)}
                          />
                        </div>
                        {formErrors.companyName && (
                          <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center gap-2 mt-2 text-red-400 text-sm"
                          >
                            <AlertCircle className="w-4 h-4" />
                            {formErrors.companyName}
                          </motion.div>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium text-white mb-2 block">Job Title</label>
                        <input 
                          type="text" 
                          placeholder="Office Manager"
                          className="w-full bg-white/10 p-3 rounded-lg border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-green-400"
                          value={formData.jobTitle || ''}
                          onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between mt-8">
                    <Button variant="outline" onClick={handleBack}>Back</Button>
                    <Button 
                      onClick={handleSubmit}
                      disabled={!isStepValid() || isLoading}
                      className="px-8 py-3 rounded-xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold shadow-lg"
                    >
                      {isLoading ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <>
                          Schedule Free Estimate <ArrowRight className="ml-2 w-5 h-5" />
                        </>
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
        
        {/* Floating Step Navigation */}
        <div className="fixed bottom-6 right-6 z-40">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-4">
            <div className="flex items-center gap-2">
              {steps.map((step) => (
                <motion.button
                  key={step.id}
                  onClick={() => {
                    if (step.id < currentStep || (step.id === currentStep + 1 && isStepValid())) {
                      setCurrentStep(step.id);
                    }
                  }}
                  disabled={step.id > currentStep + 1 || (step.id === currentStep + 1 && !isStepValid())}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    currentStep === step.id 
                      ? 'bg-green-400 scale-125' 
                      : currentStep > step.id 
                      ? 'bg-green-400/60 hover:bg-green-400/80' 
                      : 'bg-white/30 hover:bg-white/50'
                  } ${
                    step.id <= currentStep || (step.id === currentStep + 1 && isStepValid()) 
                      ? 'cursor-pointer' 
                      : 'cursor-not-allowed opacity-50'
                  }`}
                  whileHover={{ scale: currentStep >= step.id ? 1.2 : 1 }}
                  whileTap={{ scale: currentStep >= step.id ? 0.9 : 1 }}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Success Popup */}
        <AnimatePresence>
          {showSuccessPopup && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={handleClosePopup}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9, y: 20 }}
                transition={{ duration: 0.3 }}
                className="bg-slate-900 border-2 border-green-400 rounded-3xl shadow-2xl p-8 max-w-md w-full mx-4"
                onClick={(e) => e.stopPropagation()}
                style={{
                  background: 'linear-gradient(135deg, rgba(15, 32, 39, 0.98) 0%, rgba(32, 58, 67, 0.98) 50%, rgba(44, 85, 48, 0.98) 100%)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(34, 197, 94, 0.3)',
                }}
              >
                {/* Close Button */}
                <button
                  onClick={handleClosePopup}
                  className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>

                {/* Success Content */}
                <div className="text-center">
                  {/* Success Icon */}
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                    className="w-20 h-20 bg-green-400 rounded-full flex items-center justify-center mx-auto mb-6"
                  >
                    <CheckCircle className="w-10 h-10 text-white" />
                  </motion.div>

                  {/* Title */}
                  <motion.h2
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-2xl font-bold text-white mb-3"
                  >
                    Estimate Scheduled!
                  </motion.h2>

                  {/* Description */}
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="text-gray-300 mb-6 leading-relaxed"
                  >
                    Thank you for choosing our office cleaning services! {user 
                      ? 'Your estimate request has been added to your bookings dashboard where you can track its progress.'
                      : 'Sign in to access your bookings dashboard and track your estimate progress.'
                    }
                  </motion.p>

                  {/* Details */}
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-white/5 rounded-xl p-4 mb-6 text-left"
                  >
                    <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
                      <Clock className="w-4 h-4 text-green-400" />
                      What happens next?
                    </h3>
                    <ul className="space-y-2 text-sm text-gray-300">
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        {user ? 'Track your estimate status in your dashboard' : 'Sign in to track your estimate status'}
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        Our team will contact you within 24 hours
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                        Receive your customized cleaning proposal
                      </li>
                    </ul>
                  </motion.div>

                  {/* Action Button */}
                  <motion.button
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    onClick={handleClosePopup}
                    className="w-full px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-lg"
                  >
                    {user ? 'View Your Bookings' : 'Sign In to View Bookings'}
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </AnimatedBackground>
  );
};

export default BrandAlignedFloorCareForm; 
