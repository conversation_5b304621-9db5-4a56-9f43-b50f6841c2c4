import React from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowRight, <PERSON>, Star, Wind
} from 'lucide-react';

export function CommercialHero() {
  return (
    <section className="relative min-h-[85vh] flex items-center justify-center pt-20 text-white overflow-hidden"
        style={{
          backgroundImage: 'url("https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-black/40 z-0"></div>
        {/* Subtle Animated Background Elements */}
        <div className="absolute inset-0 z-1">
          {[...Array(10)].map((_, i) => (
          <motion.div
            key={i}
              className="absolute bg-white/5 rounded-full"
              style={{
                width: Math.random() * 80 + 20,
                height: Math.random() * 80 + 20,
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              initial={{ opacity: 0, scale: 0.5 }}
            animate={{
                opacity: [0, 1, 0],
                y: [0, Math.random() * 40 - 20, 0],
                x: [0, Math.random() * 40 - 20, 0],
              }}
              transition={{
                duration: 15 + Math.random() * 10,
                  repeat: Infinity,
                ease: 'easeInOut',
                delay: Math.random() * 5,
              }}
            />
        ))}
      </div>

        {/* Main Content */}
        <div className="relative z-20 w-full max-w-4xl mx-auto px-4 text-center">
            <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="text-5xl md:text-7xl font-bold leading-tight mb-6"
            style={{ color: '#ffffff', textShadow: '0 4px 12px rgba(0,0,0,0.8), 0 2px 6px rgba(0,0,0,0.6)' }}
              >
            Professional Clean,
            <br />
            Peak Performance.
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: 'easeOut' }}
            className="text-lg md:text-xl max-w-2xl mx-auto mb-10"
            style={{ color: '#ffffff', textShadow: '0 3px 8px rgba(0,0,0,0.7), 0 1px 4px rgba(0,0,0,0.5)' }}
            >
            Elevate your business environment with our reliable and efficient commercial cleaning services.
            </motion.p>

            <motion.div
            initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, ease: 'easeOut' }}
            className="flex items-center justify-center"
            >
            <motion.button
              onClick={() => {
                const servicesSection = document.getElementById('services-section');
                if (servicesSection) {
                  servicesSection.scrollIntoView({ 
                    behavior: 'smooth',
                    block: 'start'
                  });
                }
              }}
              whileHover={{ 
                scale: 1.05, 
                y: -3,
                boxShadow: '0 10px 30px rgba(255,255,255,0.3)',
                transition: { duration: 0.2 }
              }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 md:px-10 md:py-5 text-base md:text-lg font-bold rounded-xl transition-all duration-300 flex items-center gap-3 bg-emerald-600 text-white shadow-lg hover:shadow-2xl relative overflow-hidden whitespace-nowrap hover:bg-emerald-700"
              style={{
                boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.2)'
              }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/10 to-white/5 opacity-0"
                whileHover={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
              <span className="relative z-10 whitespace-nowrap text-white" style={{ color: '#ffffff !important' }}>View Services</span>
              <motion.div
                animate={{ y: [0, -2, 0] }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="relative z-10"
              >
                <motion.div
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <ArrowRight 
                    className="w-5 h-5 text-white rotate-[-90deg]" 
                    color="#ffffff" 
                    fill="none" 
                    stroke="#ffffff" 
                    strokeWidth="2"
                    style={{ color: '#ffffff' }}
                  />
                </motion.div>
              </motion.div>
            </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6, ease: 'easeOut' }}
            className="flex justify-center items-center gap-8 mt-16"
            >
              {[
              { icon: Shield, text: 'Fully Insured & Bonded' },
              { icon: Star, text: 'Customized Plans' },
              { icon: Wind, text: 'Eco-Friendly Options' }
            ].map((badge) => (
              <div key={badge.text} className="flex items-center gap-2" style={{ color: '#ffffff', textShadow: '0 2px 4px rgba(0,0,0,0.6)' }}>
                <badge.icon className="w-5 h-5" color="#ffffff" stroke="#ffffff" fill="none" />
                <span className="text-sm font-medium" style={{ color: '#ffffff' }}>{badge.text}</span>
              </div>
              ))}
            </motion.div>
        </div>
      </section>
  );
}
