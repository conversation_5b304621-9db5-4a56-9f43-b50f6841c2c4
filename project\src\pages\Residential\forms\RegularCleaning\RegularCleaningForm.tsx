import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { ProgressBar } from '../../../../components/forms/ProgressBar';
import { ServiceTypeSelector } from './components/ServiceTypeSelector';
import { PropertyDetails } from './components/PropertyDetails';
import { ServicePreferences } from './components/ServicePreferences';
import { Schedule } from './components/Schedule';
import { Contact } from './components/Contact';
import { SlideTransition } from '../../../../components/animations/SlideTransition';
import { useFormValidation } from '../../../../lib/hooks/useFormValidation';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { steps, initialFormData } from './types';
import type { FormData } from './types';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { normalizeServiceType } from '../../../../lib/services/serviceTypeRegistry';
// Import BookingService for direct booking testing
import { BookingService } from '../../../../lib/api/bookingService';


interface RegularCleaningFormProps {
  onBack: () => void;
}

export function RegularCleaningForm({ onBack }: RegularCleaningFormProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, saveFormData } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [estimatedPrice, setEstimatedPrice] = useState(0);
  // Simple validation function for steps
  const validateStep = (step: number, data: FormData) => {
    switch (step) {
      case 0: return !!data.serviceType;
      case 1: return !!(data.propertyDetails.address && data.propertyDetails.city);
      case 2: return !!(data.servicePreferences.frequency);
      case 3: return !!(data.schedule.preferredDate);
      case 4: return !!(data.contact.firstName && data.contact.email);
      default: return true;
    }
  };

  // Check for skip parameter to bypass first step
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const skipFirstStep = params.get('skip') === '1';
    
    // If skip parameter is present and we're on the first step, move to the second step
    if (skipFirstStep && currentStep === 0) {
      // Set a default service type based on the URL path
      const pathSegments = location.pathname.split('/');
      const serviceType = pathSegments[pathSegments.length - 1] || 'regular';
      
      // Update form data with the service type
      setFormData(prev => ({
        ...prev,
        serviceType
      }));
      
      // Move to the second step
      setCurrentStep(1);
    }
  }, [location, currentStep]);

  // Calculate estimated price using centralized pricing service
  useEffect(() => {
    try {
      // Normalize service type to ensure compatibility
      const normalizedServiceType = normalizeServiceType(formData.serviceType || 'residential_regular');

      // Prepare pricing input
      const pricingInput: PricingInput = {
        serviceType: normalizedServiceType,
        propertySize: formData.propertyDetails.squareFootage || 1500,
        frequency: formData.servicePreferences.frequency === 'bi-weekly' ? 'biweekly' :
                  formData.servicePreferences.frequency === 'one-time' ? 'onetime' :
                  formData.servicePreferences.frequency || 'onetime',
        addOns: formData.servicePreferences.addOns || [],
        customOptions: {
          bedrooms: formData.propertyDetails.bedrooms || 0,
          bathrooms: formData.propertyDetails.bathrooms || 0,
          propertyType: 'residential'
        }
      };

      // Calculate price using centralized service
      const pricingResult = calculatePrice(pricingInput);
      setEstimatedPrice(Math.round(pricingResult.total));

    } catch (error) {
      console.error('Error calculating price:', error);
      // Fallback to a reasonable default
      setEstimatedPrice(150);
    }
  }, [formData]);

  const handleNext = () => {
    console.log('--- Triggered handleNext ---');
    console.log(`Current Step: ${currentStep} (${steps[currentStep]})`);
    console.log('Form Data at this step:', JSON.stringify(formData, null, 2));

    const isValid = validateStep(currentStep, formData);
    console.log(`Step validation result: ${isValid}`);

    if (isValid) {
    setValidationError(null);
      if (currentStep < steps.length - 1) {
        console.log('Validation successful, moving to next step.');
        setCurrentStep((prev) => prev + 1);
      } else {
        console.log('Validation successful, proceeding to submit.');
        handleSubmit();
      }
    } else {
      const errorMessage = 'Please complete all required fields before proceeding.';
      console.error(errorMessage, 'Validation failed for step:', currentStep);
      setValidationError(errorMessage);
    }
  };

  const handlePrev = () => {
    if (currentStep === 0) {
      onBack();
    } else {
      setCurrentStep((prev) => Math.max(prev - 1, 0));
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setValidationError(null);

      if (!user) {
        // Save form data and redirect to login/signup
        saveFormData(formData);
        navigate('/auth/login', { 
          state: { from: location.pathname }
        });
        return;
      }

      // Show payment modal
      setShowPaymentModal(true);
    } catch (err) {
      console.error('Error submitting form:', err);
      setValidationError(
        err instanceof Error ? err.message : 'Failed to submit request'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // NEW: Direct booking submission to test Supabase without payment
  const handleDirectBooking = async () => {
    try {
      setIsSubmitting(true);
      setValidationError(null);
      
      console.log('🔬 TESTING: Direct booking submission bypassing payment...');
      console.log('📊 Form data:', JSON.stringify(formData, null, 2));
      console.log('👤 User:', user?.id);

      if (!user) {
        throw new Error('User must be logged in for direct booking test');
      }

      // Prepare form data in the expected format for BookingService
      const standardizedFormData = {
        // Contact info
        contact: formData.contact,
        
        // Property details
        propertyDetails: formData.propertyDetails,
        
        // Service details
        serviceType: formData.serviceType || 'regular',
        servicePreferences: formData.servicePreferences,
        
        // Schedule
        schedule: formData.schedule,
        
        // Pricing
        estimatedPrice: estimatedPrice
      };

      console.log('📋 Standardized form data:', JSON.stringify(standardizedFormData, null, 2));

      // Attempt direct booking submission to Supabase
      const bookingResult = await BookingService.saveBooking(
        standardizedFormData, 
        'residential_regular', 
        user
      );

      console.log('✅ BOOKING SUCCESS: Direct Supabase booking worked!');
      console.log('📄 Booking result:', JSON.stringify(bookingResult, null, 2));
      
      // Show success message
      setValidationError(null);
      alert(`✅ BOOKING TEST SUCCESS!\n\nBooking ID: ${bookingResult.id}\nThis confirms Supabase booking works.\nIssue is likely in payment processing.`);
      
      // Navigate to thank you page
      navigate('/thank-you', { 
        state: { 
          bookingId: bookingResult.id,
          testMode: true,
          message: 'Direct booking test successful - payment processing needs debugging'
        }
      });

    } catch (error) {
      console.error('❌ BOOKING FAILED: Direct Supabase booking failed');
      console.error('💥 Error details:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown booking error';
      
      setValidationError(`❌ BOOKING TEST FAILED:\n\n${errorMessage}\n\nThis confirms the issue is in Supabase booking, not payment processing.`);
      
      // Log detailed error for debugging
      console.log('🔍 DEBUGGING INFO:', {
        errorMessage,
        formData: standardizedFormData,
        userId: user?.id,
        errorStack: error instanceof Error ? error.stack : 'No stack trace'
      });

    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ServiceTypeSelector
            selected={formData.serviceType}
            onChange={(type) => setFormData({ ...formData, serviceType: type })}
          />
        );
      case 1:
        return (
          <PropertyDetails
            details={formData.propertyDetails}
            onChange={(details) => setFormData({ ...formData, propertyDetails: details })}
          />
        );
      case 2:
        return (
          <ServicePreferences
            preferences={formData.servicePreferences}
            onChange={(preferences) => setFormData({ ...formData, servicePreferences: preferences })}
          />
        );
      case 3:
        return (
          <Schedule
            schedule={formData.schedule}
            onChange={(schedule) => setFormData({ ...formData, schedule: schedule })}
          />
        );
      case 4:
        return (
          <Contact
            contact={formData.contact}
            onChange={(contact) => setFormData({ ...formData, contact: contact })}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8 sm:p-10">
          {/* Progress Bar */}
          <ProgressBar steps={steps} currentStep={currentStep} color="#10b981" />

          {/* Validation Error */}
          {validationError && (
            <div className="mb-8 p-5 bg-red-50/80 border border-red-200/60 rounded-2xl backdrop-blur-sm">
              <div className="flex items-start">
                <AlertCircle 
                  className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" 
                  color="#dc2626"
                  fill="none"
                  stroke="#dc2626"
                  strokeWidth="2"
                />
                <div className="text-red-700 font-medium whitespace-pre-line leading-relaxed">
                  {validationError}
                </div>
              </div>
            </div>
          )}

          {/* Price Estimate */}
          {currentStep > 1 && (
            <div className="mb-8 p-6 bg-emerald-50/80 border border-emerald-200/60 rounded-2xl backdrop-blur-sm">
              <div className="flex justify-between items-center">
                <span className="text-emerald-800 font-semibold text-lg">Estimated Price:</span>
                <span className="text-2xl font-bold text-emerald-800">${estimatedPrice}</span>
              </div>
              <p className="text-sm text-emerald-700/80 mt-2 leading-relaxed">
                Final price may vary based on inspection and additional services
              </p>
            </div>
          )}

          {/* Form Content */}
          <div className="mt-14 mb-14">
            <SlideTransition>
              <div className="transition-all duration-300 ease-in-out">
                {renderStep()}
              </div>
            </SlideTransition>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-between pt-8 border-t border-gray-100">
            <Button
              variant="outline"
              onClick={handlePrev}
              disabled={isSubmitting}
              size="lg"
              className="min-w-[120px]"
            >
              {currentStep === 0 ? 'Cancel' : 'Back'}
            </Button>
            
            <Button
              onClick={handleNext}
              disabled={isSubmitting}
              size="lg"
              className="min-w-[120px]"
            >
              {currentStep === steps.length - 1 ? 'Proceed to Payment' : 'Continue'}
            </Button>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={estimatedPrice}
        description={`${formData.serviceType.charAt(0).toUpperCase() + formData.serviceType.slice(1)} Cleaning Service`}
        customerEmail={formData.contact.email}
        formData={formData}
        user={user}
      />
    </div>
  );
}
