import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Clock, Leaf, Users, HeartHandshake, Sparkles } from 'lucide-react';

const features = [
  { icon: Shield, title: 'Trusted & Vetted', description: 'Every professional is background-checked and insured for your safety.' },
  { icon: Clock, title: 'Flexible Scheduling', description: 'Book anytime, 7 days a week, with same-day availability.' },
  { icon: Leaf, title: 'Eco-Friendly Products', description: 'We use non-toxic products that are safe for family and pets.' },
  { icon: Users, title: 'Consistent Quality', description: 'Get the same trusted and trained cleaner for every visit.' },
  { icon: HeartHandshake, title: 'Satisfaction Guaranteed', description: 'If you\'re not happy, we\'ll re-clean for free. No questions asked.' },
  { icon: Sparkles, title: 'Attention to Detail', description: 'Our 127-point checklist ensures no detail is overlooked.' }
];

export function Features() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Peace of Mind, Every Time</h2>
          <p className="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
            We are dedicated to providing a seamless and trustworthy service from start to finish.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6, ease: 'easeOut' }}
                className="flex items-start gap-6"
              >
                <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl border border-emerald-200">
                  <Icon 
                    className="w-6 h-6" 
                    color="#10b981"
                    fill="none"
                    stroke="#10b981"
                    strokeWidth="2"
                  />
                    </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-700 leading-relaxed">{feature.description}</p>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
