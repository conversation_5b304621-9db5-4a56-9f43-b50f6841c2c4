@import './styles/animations.css';
@import './styles/responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CLEAN SERVICE MARKETPLACE STYLES */
html, body {
  overflow-x: hidden;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  background-image: none;
  color: #374151;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile scroll optimization */
html {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

body {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  overscroll-behavior-y: contain;
}

/* Prevent horizontal scroll on mobile */
#root {
  overflow-x: hidden;
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
  background-image: none;
  color: #374151;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delay-2 {
  animation: float 6s ease-in-out 2s infinite;
}

.animate-pulse-scale {
  animation: pulse-scale 3s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
  opacity: 0;
}

/* Aspect ratio utilities */
.aspect-w-16 {
  position: relative;
  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);
  --tw-aspect-w: 16;
}

.aspect-h-9 {
  --tw-aspect-h: 9;
}

.aspect-w-16 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Wave animations */
@keyframes wave {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-wave {
  animation: wave 15s linear infinite;
}

.animate-wave-slow {
  animation: wave 20s linear infinite;
}

.animate-wave-slower {
  animation: wave 25s linear infinite;
}

/* Form smooth scrolling and transitions */
.form-container {
  scroll-margin-top: 2rem;
}

/* CLEAN SERVICE MARKETPLACE COMPONENTS */
.bg-white {
  background-color: #ffffff !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

/* SERVICE MARKETPLACE CARD STYLING */
.service-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.service-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
  border-color: #10b981;
}

/* SERVICE ICON STYLING */
.service-icon {
  color: #10b981;
  background-color: #ecfdf5;
  padding: 12px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

/* CLEAN TYPOGRAPHY */
.service-title {
  color: #111827;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  line-height: 1.4;
}

.service-description {
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

/* SECTION HEADERS */
.section-header {
  color: #111827;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 32px;
  text-align: center;
  line-height: 1.3;
}

.section-subheader {
  color: #6b7280;
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 40px;
  text-align: center;
  line-height: 1.5;
}

/* CLEAN BUTTON STYLING */
.btn-primary {
  background-color: #10b981;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: #ffffff;
  color: #10b981;
  border: 1px solid #10b981;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #ecfdf5;
  transform: translateY(-1px);
}

/* Soft glassmorphism */
.backdrop-blur-xl {
  backdrop-filter: blur(16px);
}

/* Clean dark text on white components - but not navigation */
.bg-white *:not(nav *):not(.text-white) {
  color: #1e293b;
}

/* Prevent layout shift during step transitions */
.step-container {
  min-height: 600px;
  transition: all 0.3s ease-in-out;
}

/* Improved grid stability for add-on services */
.addon-grid {
  display: grid;
  grid-template-rows: auto;
  align-items: stretch;
}

/* Smooth transitions for motion components */
.motion-safe {
  transform: translateZ(0);
  will-change: transform;
}

/* Mobile-specific scroll improvements */
@media (max-width: 768px) {
  body {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
  
  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }
}

/* ENHANCED FLOATING ANIMATIONS */
@keyframes float-enhanced {
  0%, 100% { 
    transform: translateY(0) rotate(0deg) scale(1); 
  }
  33% { 
    transform: translateY(-20px) rotate(2deg) scale(1.05); 
  }
  66% { 
    transform: translateY(-8px) rotate(-1deg) scale(0.98); 
  }
}

@keyframes float-reverse {
  0%, 100% { 
    transform: translateY(0) rotate(0deg) scale(1); 
  }
  33% { 
    transform: translateY(15px) rotate(-2deg) scale(0.95); 
  }
  66% { 
    transform: translateY(8px) rotate(1deg) scale(1.02); 
  }
}

.animate-float-enhanced {
  animation: float-enhanced 12s ease-in-out infinite;
}

.animate-float-enhanced-delay-2 {
  animation: float-enhanced 12s ease-in-out 2s infinite;
}

.animate-float-enhanced-delay-4 {
  animation: float-enhanced 12s ease-in-out 4s infinite;
}

.animate-float-reverse {
  animation: float-reverse 14s ease-in-out infinite;
}

.animate-float-reverse-delay-3 {
  animation: float-reverse 14s ease-in-out 3s infinite;
}

.animate-float-reverse-delay-6 {
  animation: float-reverse 14s ease-in-out 6s infinite;
}

/* ORBIT ANIMATIONS */
@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(80px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(80px) rotate(-360deg);
  }
}

@keyframes orbit-ellipse {
  0% {
    transform: rotate(0deg) translateX(120px) translateY(60px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(120px) translateY(60px) rotate(-360deg);
  }
}

.animate-orbit {
  animation: orbit 40s linear infinite;
}

.animate-orbit-reverse {
  animation: orbit 35s linear infinite reverse;
}

.animate-orbit-ellipse {
  animation: orbit-ellipse 50s linear infinite;
}

.animate-orbit-ellipse-reverse {
  animation: orbit-ellipse 45s linear infinite reverse;
}

/* ENHANCED GRADIENT ORBS */
@keyframes gradient-shift {
  0%, 100% {
    background: radial-gradient(circle, rgba(16,185,129,0.1) 0%, rgba(34,197,94,0.05) 50%, transparent 100%);
  }
  25% {
    background: radial-gradient(circle, rgba(34,197,94,0.15) 0%, rgba(74,222,128,0.08) 50%, transparent 100%);
  }
  50% {
    background: radial-gradient(circle, rgba(74,222,128,0.12) 0%, rgba(16,185,129,0.06) 50%, transparent 100%);
  }
  75% {
    background: radial-gradient(circle, rgba(22,163,74,0.18) 0%, rgba(34,197,94,0.09) 50%, transparent 100%);
  }
}

.animate-gradient-shift {
  animation: gradient-shift 8s ease-in-out infinite;
}

.animate-gradient-shift-delay-2 {
  animation: gradient-shift 8s ease-in-out 2s infinite;
}

.animate-gradient-shift-delay-4 {
  animation: gradient-shift 8s ease-in-out 4s infinite;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Service Card Hover Effects */
.service-card-hover {
  position: relative;
}

.service-card-hover .service-card-line {
  height: 0px;
  transition: height 0.3s ease;
}

.service-card-hover:hover .service-card-line {
  height: 100%;
}

/* Process Step Card Effects - Removed conflicting CSS */