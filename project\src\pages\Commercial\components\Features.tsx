import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Clock, Leaf, Users, HeartHandshake, Sparkles } from 'lucide-react';

const features = [
  { icon: Shield, title: 'Compliance & Safety', description: 'Adherence to OSHA standards, fully insured and bonded for your protection.' },
  { icon: Clock, title: 'After-Hours Service', description: 'Flexible cleaning schedules to minimize disruption to your business operations.' },
  { icon: Leaf, title: 'Green Cleaning Solutions', description: 'Optional eco-friendly and sustainable cleaning products for a healthier workplace.' },
  { icon: Users, title: 'Dedicated Account Manager', description: 'A single point of contact to ensure seamless communication and service.' },
  { icon: HeartHandshake, title: 'Service Level Agreements', description: 'Customized SLAs to guarantee quality and define service expectations.' },
  { icon: Sparkles, title: 'Advanced Disinfection', description: 'Specialized techniques and hospital-grade disinfectants for a sanitized environment.' }
];

export function Features() {
  return (
    <section className="py-24 sm:py-32">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">Your Partner in Professionalism</h2>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
            We are committed to delivering a reliable, secure, and high-quality cleaning service.
          </p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12"
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.15,
                delayChildren: 0.1
              }
            }
          }}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => {
            const Icon = feature.icon;
            
            return (
              <motion.div
                key={feature.title}
                variants={{
                  hidden: { 
                    opacity: 0, 
                    y: 50,
                    rotateX: -15
                  },
                  visible: { 
                    opacity: 1, 
                    y: 0,
                    rotateX: 0,
                    transition: {
                      type: "spring",
                      stiffness: 100,
                      damping: 15,
                      duration: 0.6
                    }
                  }
                }}
                whileHover={{ 
                  scale: 1.05, 
                  y: -8,
                  rotateY: 5,
                  transition: { 
                    type: "spring", 
                    stiffness: 300,
                    damping: 20 
                  }
                }}
                whileTap={{ scale: 0.95 }}
                className="group flex items-start gap-6 p-6 rounded-2xl bg-white border border-gray-200 shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer relative overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)'
                }}
              >
                {/* Animated background gradient on hover */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-emerald-100/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"
                  initial={false}
                />
                
                {/* Floating particles effect */}
                <div className="absolute inset-0 overflow-hidden rounded-2xl">
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-emerald-200/40 rounded-full"
                      style={{
                        left: `${20 + i * 25}%`,
                        top: `${30 + i * 15}%`,
                      }}
                      animate={{
                        y: [-10, 10, -10],
                        opacity: [0.3, 0.7, 0.3],
                        scale: [0.8, 1.2, 0.8]
                      }}
                      transition={{
                        duration: 3 + i * 0.5,
                        repeat: Infinity,
                        ease: "easeInOut",
                        delay: i * 0.5
                      }}
                    />
                  ))}
                </div>

                <motion.div 
                  className="relative z-10 flex-shrink-0 flex items-center justify-center w-12 h-12 bg-emerald-100 rounded-xl border border-emerald-200 shadow-sm group-hover:shadow-md"
                  whileHover={{ 
                    rotate: 360,
                    scale: 1.1,
                    transition: { 
                      duration: 0.6,
                      ease: "easeInOut"
                    }
                  }}
                  style={{
                    background: 'linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%)'
                  }}
                >
                  <motion.div
                    animate={{
                      scale: [1, 1.05, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  >
                    <Icon className="w-6 h-6 text-emerald-800 group-hover:text-emerald-900 transition-colors duration-300" />
                  </motion.div>
                </motion.div>
                
                <div className="relative z-10">
                  <motion.h3 
                    className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-emerald-900 transition-colors duration-300"
                    initial={false}
                    whileHover={{ x: 2 }}
                  >
                    {feature.title}
                  </motion.h3>
                  <motion.p 
                    className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300"
                    initial={false}
                    whileHover={{ x: 2 }}
                  >
                    {feature.description}
                  </motion.p>
                </div>
              </motion.div>
            );
          })}
        </motion.div>
      </div>
    </section>
  );
} 
