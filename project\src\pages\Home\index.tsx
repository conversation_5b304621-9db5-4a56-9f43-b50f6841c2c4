import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Building2, Home as HomeIcon, ArrowRight, Shield, Star, Clock, 
  Users, Sparkles, ChevronDown, CheckCircle, X, AlertCircle
} from 'lucide-react';

import { Header } from '../../components/layout/Header';
import { Footer } from '../../components/layout/Footer';
import { AnimatedBackground } from '../../components/layout/AnimatedBackground';
import { ReviewCarousel } from '../../components/testimonials/ReviewCarousel';
import ModernSearchEngine from '../../components/ModernSearchEngine';

import { navigateToServiceForm } from '../../lib/utils/navigation';

// Banner text animation options
const bannerTexts = [
  "Home cleaning, made easy.",
  "Office cleaning, made simple.",
  "Commercial spaces, made spotless.",
  "Your space, our expertise."
];

const Section: React.FC<{ children: React.ReactNode, className?: string }> = ({ children, className = '' }) => (
  <section className={`py-12 sm:py-16 lg:py-20 ${className}`}>
    {children}
  </section>
);

export function HomePage() {
  const navigate = useNavigate();
  const location = useLocation();

  const [showFAQ, setShowFAQ] = useState<number | null>(null);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [showMessage, setShowMessage] = useState(false);
  const [message, setMessage] = useState('');

  // Check for redirect messages
  useEffect(() => {
    if (location.state?.message) {
      setMessage(location.state.message);
      setShowMessage(true);
      
      // Clear the message from location state
      navigate(location.pathname, { replace: true });
      
      // Auto-hide message after 8 seconds
      setTimeout(() => {
        setShowMessage(false);
      }, 8000);
    }
  }, [location.state, navigate, location.pathname]);

  // Banner text animation
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBannerIndex((prevIndex) => (prevIndex + 1) % bannerTexts.length);
    }, 4000); // Change every 4 seconds

    return () => clearInterval(interval);
  }, []);

  const faqs = [
    {
      question: "How often should I schedule cleaning services?",
      answer: "For residential properties, we recommend weekly or bi-weekly cleaning for high-traffic homes, and monthly for less frequently used spaces. For commercial properties, daily or weekly cleaning is typically recommended depending on your business type and foot traffic."
    },
    {
      question: "Do you bring your own cleaning supplies?",
      answer: "Yes, our professional team brings all necessary cleaning supplies and equipment. We use eco-friendly, commercial-grade products that are effective yet safe for your family, pets, and the environment."
    },
    {
      question: "Are your cleaning staff insured and background checked?",
      answer: "Absolutely. All our cleaning professionals undergo thorough background checks and are fully insured. We prioritize your security and peace of mind with every service."
    },
    {
      question: "What if I'm not satisfied with the cleaning service?",
      answer: "Your satisfaction is our priority. If you're not completely satisfied with our service, contact us within 24 hours and we'll return to re-clean the areas in question at no additional cost."
    }
  ];

  return (
    <AnimatedBackground>
      <div className="relative h-screen overflow-hidden">
        <Header />
        
        <div className="h-full overflow-y-auto" style={{ paddingTop: '80px' }}>
          <AnimatePresence>
            {showMessage && (
              <motion.div
                initial={{ opacity: 0, y: -100 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -100 }}
                className="fixed top-24 left-0 right-0 z-50 mx-4"
              >
                <div className="max-w-4xl mx-auto">
                  <div className="bg-orange-100 border border-orange-300 rounded-lg p-4 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <AlertCircle className="w-5 h-5 text-orange-600 mr-3" />
                        <p className="text-orange-800 font-medium">{message}</p>
                      </div>
                      <button
                        onClick={() => setShowMessage(false)}
                        className="text-orange-600 hover:text-orange-800 transition-colors"
                      >
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          
          <main>
            {/* Hero Section */}
            <Section className="!pt-12 md:!pt-20">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                  <motion.div 
                    className="mb-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, ease: 'easeOut' }}
                  >
                    <AnimatePresence mode="wait">
                      <motion.h1
                        key={currentBannerIndex}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -30 }}
                        transition={{ duration: 0.5, ease: 'easeInOut' }}
                        className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight"
                      >
                        {bannerTexts[currentBannerIndex]}
                      </motion.h1>
                    </AnimatePresence>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.8 }}
                  >
                    <ModernSearchEngine />
                  </motion.div>
                </div>
              </div>
            </Section>

            {/* Popular Services Section */}
            <Section className="!pb-8">
              <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="mb-12">
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.6 }}
                    className="text-center mb-12"
                  >
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                      Popular Cleaning Services
                    </h2>
                    <div className="w-16 h-1 bg-emerald-800 mx-auto mb-6 rounded-full" />
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                      Choose from our most requested professional cleaning solutions
                    </p>
                  </motion.div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
                  {[
                      { icon: HomeIcon, title: "House Cleaning", description: "Regular home cleaning", id: "regular" },
                      { icon: Sparkles, title: "Deep Cleaning", description: "Thorough deep clean", id: "deep" },
                      { icon: Building2, title: "Office Cleaning", description: "Commercial spaces", id: "office" },
                      { icon: Shield, title: "Sanitization", description: "Disinfection services", id: "sanitization" },
                      { icon: Users, title: "Post-Construction", description: "Construction cleanup", id: "construction" },
                      { icon: Clock, title: "Carpet Cleaning", description: "Carpet & upholstery", id: "carpet" },
                      { icon: Star, title: "Window Cleaning", description: "Interior & exterior", id: "window" },
                      { icon: Building2, title: "Floor Care", description: "Floor restoration", id: "floor" }
                  ].map((service, index) => {
                    const Icon = service.icon;
                    return (
                        <motion.button
                          key={service.id}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true }}
                          transition={{ delay: index * 0.05 }}
                          onClick={() => navigateToServiceForm(navigate, service.id)}
                          className="service-card-hover relative group bg-white rounded-2xl p-5 text-left 
                                     border border-gray-200/60 hover:border-emerald-200 transition-all duration-300
                                     hover:bg-gray-50/50 hover:-translate-y-1 shadow-sm hover:shadow-md hover:shadow-emerald-100/50 min-h-[140px] flex items-stretch"
                        >
                          <div 
                            className="service-card-line absolute top-0 left-0 w-1 bg-gradient-to-b from-emerald-700 to-emerald-800 rounded-l-2xl"
                          />
                          <div className="flex flex-col h-full w-full">
                            <div className="relative w-12 h-12 rounded-xl bg-emerald-100 flex items-center justify-center mb-4
                                          group-hover:bg-emerald-200 transition-all duration-300 group-hover:scale-105 border border-emerald-200 group-hover:border-emerald-300">
                              <Icon className="w-6 h-6 text-emerald-800 group-hover:text-emerald-900 transition-colors" />
                              <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-emerald-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                            </div>
                            <div className="flex flex-col flex-1">
                              <h3 className="font-semibold text-gray-900 text-base mb-1.5 leading-tight">
                                {service.title}
                              </h3>
                              <p className="text-xs text-gray-500 leading-relaxed mb-3">
                                {service.description}
                              </p>
                              <div className="mt-auto">
                                <div className="flex items-center text-emerald-800 group-hover:text-emerald-900 transition-colors">
                                  <span className="text-xs font-medium">Book now</span>
                                  <ArrowRight className="w-3 h-3 ml-1 group-hover:translate-x-0.5 transition-transform" />
                                </div>
                                <div className="h-0.5 bg-emerald-800 w-0 group-hover:w-full transition-all duration-300 mt-1 rounded-full" />
                              </div>
                            </div>
                        </div>
                        </motion.button>
                    );
                  })}
                  </div>
                </div>

                {/* See more services link */}
                <div className="text-center mt-8">
                  <button
                    onClick={() => navigate('/services')}
                    className="bg-emerald-800 text-white px-8 py-3.5 rounded-xl font-medium inline-flex items-center gap-2 transition-all hover:bg-emerald-900 hover:scale-[1.02] shadow-sm hover:shadow-md hover:shadow-emerald-300"
                  >
                    See all services
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            </Section>

            {/* Explore Cleaning Projects Section */}
            <Section className="!py-0">
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-12"
                    >
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Explore Cleaning Transformations
                  </h2>
                  <div className="w-16 h-1 bg-emerald-600 mx-auto mb-6 rounded-full" />
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    See how our professional cleaning services can transform your spaces
                  </p>
                  </motion.div>

                {/* Project Cards Grid */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {[
                    {
                      title: "Residential Deep Clean",
                      description: "Complete home transformation",
                      image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Living Spaces", "Bedrooms", "Kitchens"]
                    },
                    {
                      title: "Office Makeover",
                      description: "Professional workspace cleaning",
                      image: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Desks", "Meeting Rooms", "Common Areas"]
                    },
                    {
                      title: "Move-In Ready",
                      description: "Fresh start for new beginnings",
                      image: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
                      tags: ["Empty Homes", "Deep Sanitization", "Ready to Live"]
                    }
                  ].map((project, index) => (
                  <motion.div
                      key={project.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      className="group relative overflow-hidden rounded-3xl shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer border border-gray-100 hover:border-gray-200 bg-white"
                      onClick={() => navigate('/services')}
                    >
                      {/* Image Container */}
                      <div className="relative h-80 overflow-hidden">
                          <img
                          src={project.image} 
                          alt={project.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                        {/* Enhanced Gradient Overlay for Better Text Visibility */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-black/20" />
                        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/95 to-transparent" />
                          </div>
                          
                      {/* Content Overlay */}
                      <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <h3 className="text-2xl font-bold mb-2 text-white drop-shadow-lg">{project.title}</h3>
                        <p className="text-white mb-4 drop-shadow-md">{project.description}</p>
                        
                        {/* Tags */}
                        <div className="flex flex-wrap gap-2">
                          {project.tags.map((tag) => (
                            <span 
                              key={tag}
                              className="px-3 py-1.5 bg-emerald-800/90 backdrop-blur-sm rounded-full text-xs font-medium text-white border border-emerald-700/40 shadow-lg shadow-emerald-900/20"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Hover Arrow */}
                      <div className="absolute top-4 right-4 w-12 h-12 bg-emerald-800/90 backdrop-blur-md rounded-full 
                                    flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 border border-emerald-700/30 shadow-lg shadow-emerald-900/20">
                        <ArrowRight className="w-5 h-5 text-white" />
                      </div>
                  </motion.div>
                  ))}
                </div>

                {/* View All Link */}
                <motion.div 
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ delay: 0.4 }}
                  className="text-center mt-12"
                >
                  <button
                    onClick={() => navigate('/services')}
                    className="bg-emerald-800 hover:bg-emerald-900 text-white px-6 py-3 rounded-2xl font-medium inline-flex items-center gap-2 transition-all hover:shadow-md hover:shadow-emerald-300 shadow-sm"
                  >
                    View all cleaning services
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </motion.div>
              </div>
            </Section>

            {/* What Makes Us Different Section */}
            <Section>
              <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-16"
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    What Makes Us Different
                  </h2>
                  <div className="w-16 h-1 bg-emerald-600 mx-auto mb-6 rounded-full" />
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Experience the perfect blend of technology, expertise, and care that sets us apart
                  </p>
                </motion.div>

                {/* Feature Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                  {[
                    {
                      title: "Smart Scheduling",
                      description: "Book, reschedule, or cancel anytime with our intuitive online platform.",
                      icon: Clock,
                      features: ["Real-time availability", "SMS reminders", "Flexible timing"]
                    },
                    {
                      title: "Eco-Certified Clean",
                      description: "Green Seal certified products that are safe for your family and pets.",
                      icon: Sparkles,
                      features: ["Non-toxic formulas", "Biodegradable", "Allergen-free"]
                    },
                    {
                      title: "Guaranteed Results",
                      description: "Our thorough 50-point checklist ensures nothing is missed.",
                      icon: CheckCircle,
                      features: ["Quality inspections", "Satisfaction guarantee", "24hr re-clean policy"]
                    }
                  ].map((item, index) => (
                    <motion.div
                      key={item.title}
                      initial={{ opacity: 0, y: 30 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ 
                        delay: index * 0.1,
                        duration: 0.5,
                      }}
                      className="relative group"
                    >
                      <motion.div 
                        whileHover={{ y: -8, transition: { duration: 0.3 } }}
                        className="relative bg-white rounded-3xl p-8 h-full border border-gray-100 transition-all duration-300 group-hover:border-emerald-200 group-hover:shadow-lg group-hover:shadow-emerald-100/50 shadow-sm"
                      >
                        <div className="relative inline-flex items-center justify-center w-14 h-14 bg-emerald-100 rounded-2xl mb-6 group-hover:bg-emerald-200 transition-all duration-300 border border-emerald-200 group-hover:border-emerald-300 group-hover:scale-105">
                          <item.icon className="w-7 h-7 text-emerald-800 group-hover:text-emerald-900 transition-colors" />
                          <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-emerald-400/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </div>
                        
                        <h3 className="text-xl font-bold text-gray-900 mb-3">{item.title}</h3>
                        <p className="text-gray-600 mb-6">{item.description}</p>
                        
                        <ul className="space-y-3">
                          {item.features.map((feature) => (
                            <li 
                              key={feature}
                              className="flex items-center text-sm text-gray-700"
                            >
                              <CheckCircle className="w-4 h-4 mr-3 flex-shrink-0 text-emerald-800" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Section>

            {/* Process Timeline */}
            <Section>
              <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                      Our Proven Process
                    </h2>
                    <div className="w-16 h-1 bg-emerald-800 mx-auto mb-6 rounded-full" />
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                      Four simple steps to a spotless space.
                    </p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                    {[
                      { step: "01", title: "Book Online", desc: "Select your service and schedule a time in 60 seconds.", icon: "🔍" },
                      { step: "02", title: "We Clean", desc: "Our certified professionals arrive on time and get to work.", icon: "✨" },
                      { step: "03", title: "You Relax", desc: "Enjoy your sparkling clean space and newfound free time.", icon: "😊" },
                      { step: "04", title: "Rate Us", desc: "Provide feedback so we can continue to improve our service.", icon: "⭐" }
                    ].map((process, index) => (
                      <motion.div 
                        key={process.step} 
                        initial={{ opacity: 0, y: 50 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        viewport={{ once: true, amount: 0.5 }}
                        transition={{ delay: index * 0.1, duration: 0.5 }}
                        whileHover={{ y: -8, scale: 1.02 }}
                        className="text-center group relative cursor-pointer p-6 rounded-2xl hover:bg-emerald-50/30 hover:shadow-lg hover:shadow-emerald-100/50 transition-all duration-300"
                      >
                        
                        <div className="relative">
                          <motion.div
                            whileHover={{ 
                              scale: 1.1, 
                              rotate: 2
                            }}
                            transition={{ 
                              type: "spring", 
                              stiffness: 400, 
                              damping: 15
                            }}
                            className="w-16 h-16 bg-emerald-100 border-2 border-emerald-200 rounded-2xl flex items-center justify-center shadow-sm mx-auto
                                          group-hover:border-emerald-300 group-hover:shadow-lg group-hover:shadow-emerald-200/50 group-hover:bg-emerald-200 transition-all duration-300"
                          >
                            <span className="text-xl font-bold text-emerald-800 group-hover:text-emerald-900 transition-colors duration-300">{process.step}</span>
                          </motion.div>
                        </div>
                        
                        <div className="mt-6 relative">
                          <h4 className="font-semibold text-gray-900 mb-2 text-lg group-hover:text-emerald-900 transition-colors duration-300 delay-75">{process.title}</h4>
                          <p className="text-sm text-gray-600 mb-4 group-hover:text-gray-700 transition-colors duration-300 delay-100">{process.desc}</p>
                          <div className="w-8 h-1 bg-emerald-800 mx-auto opacity-0 group-hover:opacity-100 transition-all duration-400 delay-200 rounded-full transform scale-x-0 group-hover:scale-x-100" />
                        </div>
                      </motion.div>
                    ))}
                </div>
              </div>
            </Section>

            {/* Testimonials Section - Using ReviewCarousel from Residential page */}
            <Section className="!py-0">
              <ReviewCarousel />
            </Section>

            {/* FAQ Section */}
            <Section className="!pt-8 sm:!pt-12">
              <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                  className="text-center mb-16"
                >
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Frequently Asked Questions
                  </h2>
                  <div className="w-16 h-1 bg-emerald-800 mx-auto mb-6 rounded-full" />
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Have questions? We have answers.
                  </p>
                </motion.div>
                <div className="space-y-6">
                  {faqs.map((faq, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      viewport={{ once: true }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ y: -4, scale: 1.01 }}
                      className={`bg-white border rounded-3xl overflow-hidden shadow-sm hover:shadow-lg hover:shadow-emerald-100/50 transition-all duration-300 hover:border-emerald-200 group cursor-pointer relative ${
                        showFAQ === index 
                          ? 'border-emerald-200 shadow-lg shadow-emerald-100/50' 
                          : 'border-gray-100'
                      }`}
                    >
                      {showFAQ === index && (
                        <div className="absolute left-0 top-0 w-1 h-full bg-gradient-to-b from-emerald-600 to-emerald-800 rounded-l-3xl" />
                      )}
                      <button
                        onClick={() => setShowFAQ(showFAQ === index ? null : index)}
                        className="w-full flex justify-between items-center p-8 text-left hover:bg-emerald-50/30 transition-colors duration-200"
                      >
                        <span className="text-lg font-medium text-gray-900 group-hover:text-emerald-800 transition-colors duration-200">{faq.question}</span>
                        <motion.div
                          animate={{ 
                            rotate: showFAQ === index ? 180 : 0,
                            scale: showFAQ === index ? 1.1 : 1
                          }}
                          transition={{ duration: 0.3 }}
                          className="p-1.5 rounded-full group-hover:bg-emerald-100 transition-colors duration-200"
                        >
                          <ChevronDown className={`w-5 h-5 transition-colors duration-200 ${
                            showFAQ === index ? 'text-emerald-800' : 'text-gray-500 group-hover:text-emerald-600'
                          }`} />
                        </motion.div>
                      </button>
                      <AnimatePresence>
                        {showFAQ === index && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3, ease: 'easeInOut' }}
                            className="overflow-hidden"
                          >
                            <div className="px-8 pb-8 text-gray-600 text-base leading-relaxed border-t border-emerald-100/50 bg-emerald-50/20">
                              <div className="pt-6">
                                {faq.answer}
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              </div>
            </Section>
            
            <Footer />
          </main>
        </div>
      </div>
    </AnimatedBackground>
  );
}
